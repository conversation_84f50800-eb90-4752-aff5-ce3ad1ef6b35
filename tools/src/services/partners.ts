import { wrapFetch } from '@/APIs'
import type { HLItem, PartnerOption } from '@/store/types'

export async function fetchHlItems() {
  const [err, res] = await wrapFetch<{ data: HLItem[] }>('hlItems')
  if (err || res?.code !== 0) return []
  return res.data
}

export async function fetchPartners(): Promise<PartnerOption[]> {
  const res = await fetch(`${import.meta.env.VITE_API_PREFIX}/public/data/partners.json`)
  return res.json()
}
import { useEffect, useState } from 'react'
import { Button, JsonInput } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import { wrapFetch } from '@/APIs'

import { MindMapData } from '@/components/MindMap/hooks'
import WelfareTree from '@/components/Json/ToTree'
import MindMap from '@/components/MindMap'
import { genWelfareMapData, updateWelfareRuleWithMappings, Welfare } from '@/components/MindMap/welfareHelper'

const MappingManager = () => {
  const [welfare, setWelfare] = useState<null|Welfare>(null)
  const [welfareDataString, setWelfareString] = useState('')
  const [selectedPath, setSelectedPath] = useState<string[]>([])
  const [mindMapDataGroup, setMindMaps] = useState<MindMapData[]>([])

  const onSelectNode = (node: string[]) => {
    try {
      const keys = node[0]?.split('.').slice(1)
      setSelectedPath(keys)

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const selectedValue = keys.reduce((obj: any, key) => obj?.[key], welfare)
      setWelfareString(JSON.stringify(selectedValue, null, 2))
    } catch (error) {
      console.error('解析失败:', error)
    }
  }

  const onChangeWelfare = () => {
    try {
      const newValue = JSON.parse(welfareDataString)
      if(!selectedPath.length) {
        setWelfare(newValue)
        return
      }
      const keys = [...selectedPath]
      const lastKey = keys.pop()
      
      if (!lastKey || !welfare) return
      
      const newWelfare = JSON.parse(JSON.stringify(welfare))
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const parent = keys.reduce((obj: any, key) => obj?.[key], newWelfare)
      if (parent && typeof parent === 'object') {
        parent[lastKey] = newValue
        setWelfare(newWelfare)
      }
    } catch (error) {
      console.error('更新失败:', error)
    }
  }

  useEffect(() => {
    const welfare = localStorage.getItem('welfare')
    if (welfare) {
      const welfareRule = JSON.parse(welfare)
      const mindMapDataGroup = genWelfareMapData(welfareRule)
      setMindMaps(mindMapDataGroup)
      try {
        const newWelfare = updateWelfareRuleWithMappings(welfareRule, mindMapDataGroup)
        setWelfare(newWelfare)
      } catch (error) {
        notifications.show({
          title: '解析失败',
          message: JSON.stringify(error),
          color: 'red'
        })
      }
    }
  }, [])

  const onChange = (data: MindMapData) => {
    console.log('data', data)
  }

  const onUpdateWelfareToServer = async () => {
    const result = confirm('确认无误，保存方案？')
    if (result && welfare) {
      const updateData = {
        voucher: {
          productid: welfare.id,
          rule: welfare.rules[0],
          ruleMaps: welfare.ruleMaps
        },
        express: {
          pack: {
            id: welfare.id,
            rules: welfare.rules,
            vendorMaps: welfare.vendorMaps
          }
        }
      }
      const requestData = {
        modifyMessage: '',
        modifyTags: ['替换为新版好啦最小项id'],
        ...(updateData[welfare.type as keyof typeof updateData] || {})
      }

      const url = welfare.type === 'voucher' ? 'modifyVoucher' : 'modifyExpress'
      const [err, res] = await wrapFetch(url, { requestData })
      if (err || res?.code !== 0) {
        notifications.show({
          title: '保存失败',
          message: JSON.stringify(err),
          color: 'red'
        })
      } else {
        notifications.show({
          title: '保存成功',
          message: '',
          color: 'green'
        })
      }
    }
  }

  return (
    <div className='grid grid-cols-2 gap-2 ml-3'>
      <div>
        {mindMapDataGroup.map((mapData, index) => (
          <MindMap key={index} data={mapData} onChange={onChange} />
        ))}
      </div>
      <div className='grid grid-cols-2'>
        <div>
          <Button className='mb-3' variant="light" onClick={onUpdateWelfareToServer}>确认无误，上传方案</Button>
          <WelfareTree newJSON={JSON.stringify(welfare)} field='welfare' onSelect={onSelectNode} />
        </div>
        <div>
          <Button className='mb-3' variant="light" onClick={onChangeWelfare}>保存</Button>
          <JsonInput
            minRows={10}
            maxRows={30}
            value={welfareDataString}
            onChange={setWelfareString}
            validationError="Invalid JSON"
            formatOnBlur
            autosize
            styles={{
              input: {
                fontFamily: 'monospace',
              },
            }}
          />
        </div>
      </div>
    </div>
  )
}

export default MappingManager
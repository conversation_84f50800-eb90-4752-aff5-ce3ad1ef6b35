/* eslint-disable @typescript-eslint/no-explicit-any */
import { excelDateToJSDate } from '@/utils/share'
import { read, utils, writeFile } from 'xlsx'

export const excelHandler = async (file: File, callback: (...arg: any) => void) => {
  const reader = new FileReader()
  reader.readAsArrayBuffer(file)
  reader.onload = function (e) {
    const data = e.target?.result
    if (data instanceof ArrayBuffer) {
      const wb = read(new Uint8Array(data), { type: 'array' })
      const orderJSON = wb.SheetNames.map(sheetName => utils.sheet_to_json(wb.Sheets[sheetName]))
      callback(orderJSON)
    }
  }
}

export const excelsHandler = async (files: File[]) => {
  const results = {}

  const readFile = (file: File): Promise<any[]> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file)
      reader.onload = function (e) {
        const data = e.target?.result
        if (data instanceof ArrayBuffer) {
          const wb = read(new Uint8Array(data), { type: 'array' })
          const orderJSON = wb.SheetNames.map(sheetName => utils.sheet_to_json(wb.Sheets[sheetName]))
          resolve(orderJSON[0])
        }
      }
    })
  }

  for (const file of files) {
    (results as Record<string, any[]>)[file.name] = await readFile(file)
  }

  return results
}

export function mapPaymentDates(args: Record<string, any[]>) {
  const settlementCors = args['统一结算列表导出.xlsx']
  const partnerOrders = args['订单汇总导出.xlsx']

  // 按消费月份和团单号分组
  const groupedByMonth = settlementCors.reduce((acc, item) => {
    const key = `${item['消费月份']}_${item['团单号']}`
    if (!acc[key]) {
      acc[key] = item
    }
    return acc
  }, {} as Record<string, any>)

  const qijianHeaders = ['订单总金额', '订单号', '下单时间']
  partnerOrders.sort((x, y) => (new Date(x['下单时间']).getTime() - new Date(y['下单时间']).getTime()))

  // 处理企健订单导出表
  return partnerOrders.map(item => {
    // 将下单时间转换为年-月格式
    const orderDate = new Date(item['下单时间'])
    const monthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`

    // 构建查找键查匹配记录，企健投保单位名称同一家有可能不一致。
    const lookupKey = `${monthKey}_${item['团单号']}`
    const matchedRecord = groupedByMonth[lookupKey]

    return {
      '投保单位': matchedRecord?.['投保单位'] || item['投保单位'],
      '消费月份': monthKey,
      '结算时间': matchedRecord?.['付款时间'] || '',
      ...filterData(item, qijianHeaders),
    }
  })
}

interface OrderRecord {
  [key: string]: string | number
  '下单日期': string | number
  '结算时间': string | number
}
function createEmptyRecord(template: OrderRecord): OrderRecord {
  return Object.keys(template).reduce((obj, key) => {
    obj[key] = ''
    return obj
  }, {} as OrderRecord)
}

export function mergeOrdersByDate(haolaOrders: any[], partnerOrders: any[]) {
  const tableData = []
  const hlEmpty = createEmptyRecord(haolaOrders[0])
  const qijianEmpty = createEmptyRecord(partnerOrders[0])
  let pIndex = 0, hIndex = 0
  while (pIndex < partnerOrders.length || hIndex < haolaOrders.length) {
    const partnerOrder = partnerOrders[pIndex]
    const haolaOrder = haolaOrders[hIndex]

    // 如果其中一个数组已经遍历完，将另一个数组剩余数据添加到结果中
    if (!partnerOrder || !haolaOrder) {
      tableData.push({
        ...partnerOrder || qijianEmpty,
        ...haolaOrder || hlEmpty,
      })
      pIndex++
      hIndex++
      continue
    }

    const hlDate = new Date(haolaOrder['下单日期']).getTime()
    const partnerDate = new Date(partnerOrder['下单时间']).getTime()

    const TIME_TOLERANCE = 2000 // 2秒的容差（毫秒）
    const timeDiff = Math.abs(hlDate - partnerDate)
    if (timeDiff <= TIME_TOLERANCE) {
      tableData.push({
        ...partnerOrder,
        ...haolaOrder,
      })
      pIndex++
      hIndex++
    } else if (hlDate < partnerDate) {
      // 企健订单日期更晚，在企健订单中插入空行, 好啦订单继续插入，增加好啦索引
      tableData.push({
        ...qijianEmpty,
        ...haolaOrder,
      })
      hIndex++
    } else {
      // 好啦订单日期更晚，在好啦订单中插入空行，企健订单继续插入，增加企健索引
      tableData.push({
        ...partnerOrder,
        ...hlEmpty,
      })
      pIndex++
    }
  }
  return tableData
}

/**
 * 
*/
export function matchInvoiceWithOrders(invoices: Record<string, any>[], orders: Record<string, any>[], markMap: Record<string, string>, invoiceDates: Date[]) {
  // 按公司和消费月份分组，计算订单总金额
  const orderSummary = orders.reduce((acc, order) => {
    if (!order['结算时间']) return acc

    const orderDate = new Date(order['下单时间'])
    const monthKey = `${orderDate.getFullYear()}-${String(orderDate.getMonth() + 1).padStart(2, '0')}`
    const key = `${order['投保单位']}_${monthKey}`

    if (!acc[key]) {
      acc[key] = {
        company: order['投保单位'],
        month: monthKey,
        total: 0,
        settlementTime: order['结算时间']
      }
    }
    acc[key].total += Number(order['订单总金额']) || 0
    return acc
  }, {} as Record<string, { company: string; month: string; total: number; settlementTime: string }>)

  const invoiceDateMap = invoiceDates.reduce((obj, v) => {
    obj[v.toLocaleDateString('zh-CN')] = v
    return obj
  }, {} as Record<string, Date>)

  // 处理发票记录
  return invoices
    .filter((v: { [x: string]: unknown }) => {
      if (v['归属渠道'] !== '企健') {
        return false
      }
      const dateStr = typeof v['开票日期'] === 'string'
        ? new Date(v['开票日期']).toLocaleDateString('zh-CN')
        : excelDateToJSDate(v['开票日期'] as number).toLocaleDateString('zh-CN')
      
      return !!invoiceDateMap[dateStr]
    })
    .map(invoice => {
      const invoiceDate = typeof invoice['开票日期'] === 'string'
      ? new Date(invoice['开票日期'])
      : excelDateToJSDate(invoice['开票日期'] as number)

      let settlementStatus = invoice['是否结算']
      if (invoice['是否结算'] === '是') {
        return {
         ...invoice,
          '开票日期': invoiceDate.toLocaleDateString(),
          '收款时间': typeof invoice['收款时间'] === 'number' 
            ? excelDateToJSDate(invoice['收款时间']).toLocaleDateString()
            : invoice['收款时间'] || '',
        }
      }

      // 计算消费月份（开票日期的上个月）
      const prevMonth = new Date(invoiceDate)
      prevMonth.setMonth(invoiceDate.getMonth() - 1)
      const consumeMonthKey = `${prevMonth.getFullYear()}-${String(prevMonth.getMonth() + 1).padStart(2, '0')}`
      const lookupKey = `${invoice['公司名称']}_${consumeMonthKey}`
      const orderSummaryRecord = orderSummary[lookupKey]

      if (orderSummaryRecord) {
        const invoiceAmount = Number(invoice['开票金额']) || 0
        if (Math.abs(invoiceAmount - orderSummaryRecord.total) < 0.01) {
          settlementStatus = '是'
        } else {
          settlementStatus = '不一致'
        }
      }

      // console.log(invoice['公司名称'], orderSummaryRecord)
      return {
        ...invoice,
        '开票日期': invoiceDate.toLocaleDateString(),
        '是否结算': settlementStatus,
        '收款时间': orderSummaryRecord?.settlementTime ? new Date(orderSummaryRecord.settlementTime).toLocaleDateString() : '',
        '摘要': markMap[orderSummaryRecord?.['settlementTime']] || '',
      }
    })
}

// export function genInvoice([invoices, qijianSettlements]: any[][], markMap: Record<string, string>, invoiceDates: Date[]) {
//   const qijianSettlementsMap = qijianSettlements.reduce((obj, v) => {
//     obj[`${v['投保单位']}_${v['消费月份']}`] = v
//     return obj
//   }, {})
//   const invoiceDateMap = invoiceDates.reduce((obj, v) => {
//     obj[v.toLocaleDateString('zh-CN')] = v
//     return obj
//   }, {} as Record<string, Date>)
//   const result = invoices
//     .filter((v: { [x: string]: unknown }) => v['归属渠道'] === '企健' && !!(invoiceDateMap[excelDateToJSDate(v['开票日期'] as number).toLocaleDateString('zh-CN')]))
//     .map((v: { [x: string]: any }) => {
//       const invoiceDate = excelDateToJSDate(v['开票日期'])
//       if (v['是否结算'] === '是') {
//         return {
//           ...v,
//           '开票日期': invoiceDate.toLocaleDateString('zh-CN'),
//           '收款时间': excelDateToJSDate(v['收款时间']).toLocaleDateString('zh-CN'),
//         }
//       }

//       //开票日期总是比消费月份晚一个月，所以月份需要减一
//       const yearMonth = `${invoiceDate.getFullYear()}-${String(invoiceDate.getMonth() + 1 - 1).padStart(2, '0')}`
//       const qijianSettlement = qijianSettlementsMap[`${v['公司名称']}_${yearMonth}`]

//       return {
//         ...v,
//         '开票日期': excelDateToJSDate(v['开票日期']).toLocaleDateString(),
//         '是否结算': qijianSettlement ? '是' : '否',
//         '收款时间': qijianSettlement?.['付款时间'].replaceAll('-', '/') || '',
//         '摘要': markMap[qijianSettlement?.['付款时间']] || ''
//       }
//     })
//   return result
// }

function filterData(order: Record<string, string>, header: string[]) {
  if (!order) return {}
  return header.reduce((obj, name) => {
    obj[name] = order[name]
    return obj
  }, {} as Record<string, string>)
}

export function downloadJsonExcel(data: any[], headers: string[], name: string = '企健结算') {
  const result = data.map((v: Record<string, string>) => filterData(v, headers))
  const worksheet = utils.json_to_sheet(result)
  const workbook = utils.book_new()
  utils.book_append_sheet(workbook, worksheet)
  writeFile(workbook, `${name}${new Date().toLocaleDateString()}.xlsx`)
}
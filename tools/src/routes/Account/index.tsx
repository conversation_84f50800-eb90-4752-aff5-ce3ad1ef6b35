/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useRef, useState } from 'react'
import { Fieldset, Button, TagsInput, Tabs } from '@mantine/core'
import { DateFormatter, DatePickerInput } from '@mantine/dates'
import { notifications } from '@mantine/notifications'

import Drop from './components/Drop'
import OrderForm from './components/OrderForm'

import { matchInvoiceWithOrders, downloadJsonExcel, excelsHandler, mapPaymentDates, mergeOrdersByDate } from './excelHandler'
import Editable from './components/Editable'

const Account = () => {

  //企健订单填入好啦订单号，生成具有【投保单位名称、好啦订单号、下单时间、金额等】信息的表格。目前只能用下单时间比对。
  const [mergeOrders, setMergeOrders] = useState<(Record<string, string>)[]>([])
  const [haolaOrders, setHLOrders] = useState<(Record<string, string>)[]>([])

  //生成具有【投保单位名称、开票日期、结算日期、金额、备注等】信息的表格。
  const invoiceFileData = useRef<Record<string, string>[]>([])
  //开票追踪表，摘要映射。格式为字符串数组：2023-01-02:汇入外****************
  const [remittance, setRemittance] = useState<string[]>(['1970-01-01:031500'])
  //选择开票日期
  const [invoiceDatesFilter, setInvoiceDates] = useState<Date[]>([])
  const [invoiceAndSettlementData, setSettlements] = useState<(Record<string, string>)[]>([])

  //上传文件
  const [files, setFiles] = useState<File[]>([])
  const [invoiceFiles, setInvoiceFiles] = useState<File[]>([])

  useEffect(() => {
    const partnerHandler = async () => {
      if (!haolaOrders.length || !files.length) return []
      const results = await excelsHandler(files)
      const invoiceFileName = Object.keys(results).find(key => /开票及收款核对/.test(key))
      if (invoiceFileName) {
        invoiceFileData.current = results[invoiceFileName as keyof typeof results]
      }
      const partnerOrders = mapPaymentDates(results)
      setMergeOrders(mergeOrdersByDate(haolaOrders, partnerOrders))
    }

    partnerHandler()
  }, [files, haolaOrders])

  useEffect(() => {
    const invoiceHandler = async () => {
      const results = await excelsHandler(invoiceFiles)
      const invoiceFileName = Object.keys(results).find(key => /开票及收款核对/.test(key))
      if (invoiceFileName) {
        invoiceFileData.current = results[invoiceFileName as keyof typeof results]
      }
    }
    invoiceHandler()
  }, [invoiceFiles])

  //下载合并并修改正确的订单
  const downloadMergeOrders = () => {
    const headers = Object.entries(mergeOrders[0])
    downloadJsonExcel(mergeOrders, headers.map((v) => v[0]))
  }


  const onTransfer3 = () => {
    if (!invoiceFileData.current) {
      notifications.show({
        message: '请先上传文件: 开票及收款核对.xlsx',
        color: 'red',
      })
      return
    }

    const markMap = remittance.reduce((obj, v) => {
      const [date, mark] = v.split(':')
      obj[date] = mark
      return obj
    }, {} as Record<string, string>)

    const data = matchInvoiceWithOrders(invoiceFileData.current, mergeOrders, markMap, invoiceDatesFilter)
    setSettlements(data)
  }

  const downloadSettleData3 = () => {
    const headers = Object.entries(invoiceAndSettlementData[0])
    downloadJsonExcel(invoiceAndSettlementData, headers.map((v) => v[0]), '企健近期发票收款核对')
  }

  const valueFormatter: DateFormatter = ({ type, date }) => {
    if (type === 'multiple' && Array.isArray(date)) {
      return date.map((v) => v && v.toLocaleDateString('zh-CN')).join(', ')
    }
    return ''
  }
  return (
    <>
      <Tabs className='mb-5' defaultValue="订单号映射">
        <Tabs.List className='mb-5'>
          <Tabs.Tab value="订单号映射">
            企健-好啦订单号映射表
          </Tabs.Tab>
          <Tabs.Tab value="开票追踪">
            开票追踪表
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="订单号映射">
          <div className='grid grid-cols-3 gap-5 my-4'>
            <Fieldset legend="好啦订单查询">
              <OrderForm onSubmit={setHLOrders} />
            </Fieldset>
            <Drop descr="《企健订单汇总》、《商户结算表》" files={files} setFiles={setFiles} className='col-span-1' />
          </div>
          <Button variant="light" onClick={downloadMergeOrders}>导出下表</Button>
          <Editable caption="企健订单表" table={mergeOrders} onUpdate={setMergeOrders} editable={true} />
        </Tabs.Panel>

        <Tabs.Panel value="开票追踪">
          <div className='grid grid-cols-3 gap-5 mt-4'>
            <Fieldset legend="开票信息">
              <DatePickerInput
                clearable
                type="multiple"
                label="选择开票日期"
                placeholder="可多选"
                valueFormatter={valueFormatter}
                value={invoiceDatesFilter}
                onChange={setInvoiceDates}
              />
              <TagsInput clearable
                className='mt-4'
                label="付款日期和摘要标签"
                description="格式：付款日期:摘要，输入后可以使用逗号、空格或竖线分隔生成标签"
                splitChars={[',', ' ', '|']}
                data={[]}
                value={remittance}
                onChange={setRemittance}
              />
            </Fieldset>
            <Drop descr='《开票及收款核对》' maxFiles={1} files={invoiceFiles} setFiles={setInvoiceFiles} />
          </div>
          <div className='flex my-5 ml-5'>
            <Button className='mr-5' variant="light" onClick={onTransfer3}>生成开票表</Button>
            <Button className='mr-5' variant="light" onClick={downloadSettleData3}>下载开票追踪表</Button>
          </div>
          <Editable caption="企健开票追踪表" table={invoiceAndSettlementData} />
        </Tabs.Panel>
      </Tabs>
    </>
  )
}

export default Account

/* eslint-disable @typescript-eslint/no-explicit-any */
import { Table, ActionIcon, TextInput, Group } from '@mantine/core'
import { useMemo, useState } from 'react'
import { TrashIcon, CheckIcon, Cross2Icon, PlusIcon } from '@radix-ui/react-icons' 

interface Props {
  className?: string
  caption?: string
  table: any[]
  onUpdate?: (newData: any[]) => void
  editable?: boolean
}

const Editable = (props: Props) => {
  const [editingCell, setEditingCell] = useState<{row: number, col: string} | null>(null)
  const [editValue, setEditValue] = useState('')

  const header = useMemo(() => {
    if (!props.table[0]) return []
    return Object.entries(props.table[0])
  }, [props.table])

  const handleEdit = (row: number, col: string, value: any) => {
    if(!props.editable) return
    setEditingCell({ row, col })
    setEditValue(String(value))
  }

  const handleAdd = (index: number) => {
    if (!props.table[0]) return
    // 创建一个空行，包含所有列但值为空
    const emptyRow = Object.keys(props.table[0]).reduce((obj, key) => {
      obj[key] = ''
      return obj
    }, {} as Record<string, any>)
    
    const newData = [...props.table]
    newData.splice(index + 1, 0, emptyRow)
    props.onUpdate?.(newData)
  }

  const handleSave = () => {
    if (!editingCell) return
    const newData = [...props.table]
    newData[editingCell.row] = {
      ...newData[editingCell.row],
      [editingCell.col]: editValue
    }
    props.onUpdate?.(newData)
    setEditingCell(null)
  }

  const handleDelete = (index: number) => {
    if (!props.table[0]) return
    const newData = [...props.table]
    newData.splice(index, 1)
    props.onUpdate?.(newData)
  }

  const ActionCell = ({rowIndex}: { rowIndex: number }) => {
    return props.editable ? (
      <Table.Td>
        <Group>
          <ActionIcon color="blue" onClick={() => handleAdd(rowIndex)} size="sm">
            <PlusIcon />
          </ActionIcon>
          <ActionIcon color="red" onClick={() => handleDelete(rowIndex)} size="sm">
            <TrashIcon />
          </ActionIcon>
        </Group>
      </Table.Td>
    ) : null
  }

  return (
    <>
      <Table captionSide="top" stickyHeader striped highlightOnHover>
        <Table.Caption>{props.caption}</Table.Caption>
        <Table.Thead>
          <Table.Tr>
            {header.map(([title], i) => (
              <Table.Th key={'th' + i}>{title}</Table.Th>
            ))}
            {props.editable && <Table.Th>操作</Table.Th>}
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {props.table.map((item, rowIndex) => (
            <Table.Tr key={rowIndex} style={{ height: '40px' }}>
              {header.map(([title], colIndex) => (
                <Table.Td key={'td' + colIndex} className='max-w-48 overflow-hidden whitespace-nowrap text-ellipsis'>
                  {editingCell?.row === rowIndex && editingCell?.col === title ? (
                    <Group>
                      <TextInput
                        value={editValue}
                        onChange={(e) => setEditValue(e.target.value)}
                        size="xs"
                      />
                      <ActionIcon color="blue" onClick={handleSave} size="sm">
                        <CheckIcon />
                      </ActionIcon>
                      <ActionIcon color="red" onClick={() => setEditingCell(null)} size="sm">
                        <Cross2Icon />
                      </ActionIcon>
                    </Group>
                  ) : (
                    <p className='list-item list-none min-h-6' onClick={() => handleEdit(rowIndex, title, item[title])}>
                      {item[title]}
                    </p>
                  )}
                </Table.Td>
              ))}
              <ActionCell rowIndex={rowIndex} />
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </>
  )
}

export default Editable
import { useState } from 'react'
import { <PERSON>, Button } from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import dayjs from 'dayjs'

import PartnerSelect from '@/components/Form/PartnerSelector'
import { wrapFetch } from '@/APIs'

import formStyles from './Form.module.css'

interface FilterFormProps {
  onSubmit: (arg: Record<string, string>[]) => void
  className?: string
}

function PartnerFilterForm({ onSubmit, className }: FilterFormProps) {
  const [selectedPartner, setSelectedPartner] = useState<string | null>('')
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null])

  const handleSubmit = async () => {
    const [startDate, endDate] = dateRange
    const filters = {
      collection: "userOrders",
      countPerPage: 500,
      condition: {
        partnerid: {
          value: {
            $in: [selectedPartner]
          }
        },
        reservationStatus: {
          value: {
            $in: ["completed", "pendingReserve", "reserved", "checked"]
          }
        },
        ...(startDate && endDate && {
          createdTime: {
            value: {
              $gte: dayjs(startDate).startOf('day').valueOf(),
              $lte: dayjs(endDate).endOf('day').valueOf()
            }
          }
        })
      }
    }
    const [err, res] = await wrapFetch<{data: Record<string, string>[]}>('queryData', { requestData: filters })
    if(err || res?.code !== 0) return
    onSubmit(res.data.map((item: Record<string, string>) => ({
      '好啦订单号': item.serviceid,
      '下单日期': new Date(item.createdTime).toLocaleString(),
    })))
  }

  return (
    <Box className={`${className} mt-5`}>
      <PartnerSelect
        label="渠道"
        inputSize='2rem'
        classNames={{ root: formStyles.DatePickerInputRoot }}
        value={selectedPartner}
        onChange={setSelectedPartner}
      />
      <DatePickerInput
        classNames={{ root: formStyles.DatePickerInputRoot }}
        label="下单日期"
        placeholder="选择日期范围"
        type="range"
        firstDayOfWeek={0}
        inputSize='2rem'
        value={dateRange}
        onChange={setDateRange}
        clearable
        style={{ marginTop: 16 }}
      />
      <Button onClick={handleSubmit} style={{ marginTop: 16 }}>
        查询
      </Button>
    </Box>
  )
}

export default PartnerFilterForm
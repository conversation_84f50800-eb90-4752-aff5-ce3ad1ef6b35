import { Group, Text } from '@mantine/core'
import { Dropzone, DropzoneProps, MS_EXCEL_MIME_TYPE } from '@mantine/dropzone'

import Upload from '@/components/icons/Upload'
import Excel from '@/components/icons/Excel'
import Error from '@/components/icons/Error'
import Delete from '@/components/icons/Delete'

type Props = Partial<DropzoneProps> & {
  files: File[]
  setFiles: (files: File[]) => void
  reject?: () => void
  descr?: string
}
export default function Drop({setFiles, reject, ...props}: Props) {
  const maxFiles = props.maxFiles || 2
  return (
    <Dropzone
      onDrop={setFiles}
      onReject={reject}
      maxSize={5 * 1024 ** 2}
      maxFiles={maxFiles}
      accept={MS_EXCEL_MIME_TYPE}
      {...props}
    >
      <Group justify="center" gap="xs" mih={80} style={{ pointerEvents: 'none' }}>
        <Dropzone.Accept>
          <Upload />
        </Dropzone.Accept>
        <Dropzone.Reject>
          <Error />
        </Dropzone.Reject>
        <Dropzone.Idle>
          <Excel />
        </Dropzone.Idle>

        <div>
          <Text size="md">
            {props.descr || 'Excel文件'}拖到这
          </Text>
          
          <Text size="sm" c="dimmed" inline mt={7}>
            数量仅限{maxFiles}个，大小≤5mb
          </Text>
          <div className='text-zinc-600 text-sm'>
              {props.files.map((file, i) =>
                <div key={i} className='flex items-center' onClick={() => setFiles(props.files.filter((f) => f.name !== file.name))}>
                  {file.name}
                  <Delete className='ml-1' />
                </div>
              )}
            </div>
        </div>
      </Group>
    </Dropzone>
  )
}
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Table } from '@mantine/core'
import { useMemo } from 'react'

interface Props {
  caption?: string
  table: any[]
}
const Data = (props: Props) => {
  const header = useMemo(() => {
    if (!props.table[0]) return []
    return Object.entries(props.table[0])
  }, [props.table])

  return (
    <>
      <Table captionSide="top">
        <Table.Caption>{props.caption}</Table.Caption>
        <Table.Thead>
          <Table.Tr>
            {
              header.map(([title], i) => <Table.Th key={'th' + i}>{title}</Table.Th>)
            }
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {
            props.table.map((item, i) => (
              <Table.Tr key={i}>
                {
                  header.map(([title], j) => <Table.Th key={'td' + j}>{item[title]}</Table.Th>)
                }
              </Table.Tr>
            ))
          }
        </Table.Tbody>
      </Table>

    </>
  )
}

export default Data

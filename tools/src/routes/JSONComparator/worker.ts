type WorkerMessage<T = unknown> = {
  script: string;
  data: T;
}

type WorkerResponse<T = unknown> = {
  success: boolean;
  result?: T;
  error?: string;
}

self.onmessage = function<T>(e: MessageEvent<WorkerMessage<T>>) {
  const { script, data } = e.data;
  
  try {
    const transform = new Function(`
      "use strict";
      ${script}
      return transform;
    `)();
    
    const result = transform(data) as T;
    self.postMessage({
      success: true,
      result
    } as WorkerResponse<T>);
  } catch (error) {
    self.postMessage({ 
      success: false, 
      error: error instanceof Error ? error.message : '未知错误'
    } as WorkerResponse<T>);
  }
};
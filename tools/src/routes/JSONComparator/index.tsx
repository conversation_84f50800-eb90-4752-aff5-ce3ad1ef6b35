import { useState, useRef, useEffect } from 'react'
import { JsonInput, Button, Text } from '@mantine/core'
import { notifications } from '@mantine/notifications'
import Editor from "@monaco-editor/react"

import JSONTree from '@/components/Json/ToTree'
import { downloadJson } from '@/utils/excel'

const JSONComparator = () => {
  const workerRef = useRef<Worker>()

  const [oldJSON, setOldJSON] = useState('')
  const [script, setScript] = useState(`function transform(data) {
    function genContractId(len = 12) {
      if (len < 10) throw new Error('len must great than 9')
      const dateString   = Date.now().toString(36).toUpperCase()
      const randomString = Math.floor(Math.random()*(36**(len-dateString.length))).toString(36).toUpperCase()
      let ret            = dateString+randomString
      for (let i = ret.length; i < len; i++) {
        ret += '0'
      }
      return ret
    }
    console.log(genContractId())
    return data;
  }`)
  const [newJSON, setNewJSON] = useState('')

  useEffect(() => {
    workerRef.current = new Worker(new URL('./worker.ts', import.meta.url))

    workerRef.current.onmessage = (e) => {
      const { success, result, error } = e.data
      if (success) {
        // 从worker执行脚本后返回的结果
        setNewJSON(JSON.stringify(result, null, 2))
        notifications.show({
          title: '执行成功',
          message: '脚本已成功执行',
          color: 'green'
        })
      } else {
        notifications.show({
          title: '执行失败',
          message: error || '未知错误',
          color: 'red'
        })
      }
    }

    return () => workerRef.current?.terminate()
  }, [])

  const executeScript = () => {
    try {
      const oldData = JSON.parse(oldJSON)
      workerRef.current?.postMessage({
        script,
        data: oldData
      })
    } catch (error) {
      notifications.show({
        title: '执行失败',
        message: error instanceof Error ? error.message : '未知错误',
        color: 'red'
      })
    }
  }

  return (
    <div className="grid grid-cols-7 h-screen gap-4 text-slate-600">
      {/* 左侧：原始 JSON */}
      <div className="col-span-2">
        <div className='mb-2 flex justify-between items-center'>
          <Text className="font-medium">待处理JSON丢到这👇🏻</Text>
          <Button variant='light' onClick={() => setOldJSON('')}>清空JSON</Button>
        </div>
        <JsonInput
          minRows={10}
          maxRows={30}
          value={oldJSON}
          onChange={setOldJSON}
          placeholder="请输入原始 JSON"
          validationError="Invalid JSON"
          formatOnBlur
          autosize
          styles={{
            input: {
              fontFamily: 'monospace',
            },
          }}
        />
      </div>

      {/* 中间：脚本输入 */}
      <div className="col-span-3">
        <div className='mb-2 grid grid-cols-5 items-center'>
          <Text className="col-span-3 font-medium">执行脚本写这👇🏻</Text>
          <Button variant='light' onClick={() => setScript('')}>清空脚本</Button>
          <Button className='ml-2' onClick={executeScript}>执行</Button>
        </div>
        <div className="h-[calc(100%-7rem)]">
          <Editor
            height="100%"
            defaultLanguage="javascript"
            value={script}
            onChange={(value) => setScript(value || '')}
            theme="vs-dark"
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              tabSize: 2,
              scrollBeyondLastLine: false,
              automaticLayout: true,
            }}
          />
        </div>

      </div>

      {/* 右侧：新的 JSON（树形展示） */}
      <div className="col-span-2">
        <div className='flex items-center justify-between mb-2'>
          <Text className="mr-2 font-medium">输出的结果在这看👇🏻</Text>
          <Button variant='light' onClick={() => downloadJson(JSON.parse(newJSON), `transform_${new Date().toJSON()}.json`)}>下载JSON</Button>
        </div>
        <div className="min-h-48 max-h-screen overflow-scroll border rounded p-2">
          <JSONTree
            newJSON={newJSON}
          />
        </div>
      </div>
    </div>
  )
}

export default JSONComparator
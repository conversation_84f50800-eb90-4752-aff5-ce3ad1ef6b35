import { Accordion, List } from '@mantine/core'
import { useAtom } from "jotai"

import { partnerGroupsAtom } from "@/store/partners"

import Copy from '@/components/icons/Copy'
import { copyToClipboard } from '@/utils/share'

const Entries = () => {
  const [groups] = useAtom(partnerGroupsAtom)

  const handler = (text: string) => {
    copyToClipboard(`https://www.ihaola.com.cn/launch/${text}/pe`)
  }

  return (
    <>
      <h2 className='ml-5 text-xl font-medium text-zinc-700'>各渠道体检入口链接</h2>
      <p className='ml-5 text-base font-medium text-zinc-400'>点击列表展开</p>
      <Accordion  defaultValue={groups[1]?.group}>
        {groups.map(item => (
          <Accordion.Item key={item.group} value={item.group}>
            <Accordion.Control>{item.group}</Accordion.Control>
            <Accordion.Panel>
              <List>
                {item.items.map(child => (
                  <List.Item key={`${item.group}-${child.value}`}>
                    <div className='flex my-2 cursor-pointer items-center' onClick={() => handler(child.name)}>
                      <div>{child.label}</div>
                      <div className='mx-3 underline text-blue-400'>{`https://www.ihaola.com.cn/launch/${child.name}/pe`}</div>
                      <Copy className='size-4' />
                    </div>
                  </List.Item>
                ))}
              </List>
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </Accordion>
    </>
  )
}

export default Entries
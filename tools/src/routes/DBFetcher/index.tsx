/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react'
import {
  Box,
  Select,
  TextInput,
  Button,
  Group,
  Paper,
  Title,
  Loader,
  Text,
  Stack,
  NumberInput,
  Switch,
  Accordion,
  Badge
} from '@mantine/core'
import { fetchMongoDBData, fetchCollections, fetchCollectionSchema } from './DBService'

import JSO<PERSON><PERSON> from '@/components/Json/ToTree'

interface AdvancedQueryParam {
  key: string
  value: string
  type: 'string' | 'number' | 'boolean'
}

const AdvancedMongoDBFetcher = () => {
  const [collections, setCollections] = useState<string[]>([])
  const [selectedCollection, setSelectedCollection] = useState<string>('')
  const [queryParams, setQueryParams] = useState<AdvancedQueryParam[]>([
    { key: '', value: '', type: 'string' }
  ])
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingCollections, setIsLoadingCollections] = useState(false)
  const [data, setData] = useState<any[] | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [availableFields, setAvailableFields] = useState<string[]>([])

  // 加载集合列表
  useEffect(() => {
    const loadCollections = async () => {
      setIsLoadingCollections(true)
      try {
        const collectionsData = await fetchCollections()
        setCollections(collectionsData)
        if (collectionsData.length > 0 && !selectedCollection) {
          setSelectedCollection(collectionsData[0])
        }
      } catch (err) {
        setError('加载集合列表失败')
      } finally {
        setIsLoadingCollections(false)
      }
    }

    loadCollections()
  }, [])

  // 当选择的集合改变时，加载该集合的字段信息
  useEffect(() => {
    if (!selectedCollection) return

    const loadSchema = async () => {
      try {
        const schema = await fetchCollectionSchema(selectedCollection)
        setAvailableFields(Object.keys(schema))
      } catch (err) {
        console.error('加载集合架构失败', err)
      }
    }

    loadSchema()
  }, [selectedCollection])

  const addQueryParam = () => {
    setQueryParams([...queryParams, { key: '', value: '', type: 'string' }])
  }

  const removeQueryParam = (index: number) => {
    const newParams = [...queryParams]
    newParams.splice(index, 1)
    setQueryParams(newParams)
  }

  const updateQueryParam = (index: number, field: keyof AdvancedQueryParam, value: string | 'string' | 'number' | 'boolean') => {
    const newParams = [...queryParams]
    if (field === 'type') {
      newParams[index][field] = value as 'string' | 'number' | 'boolean'
    } else {
      newParams[index][field as 'key' | 'value'] = value as string
    }
    setQueryParams(newParams)
  }

  const fetchData = async () => {
    if (!selectedCollection) {
      setError('请选择一个集合')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // 构建查询参数
      const queryObject: Record<string, any> = {
        
      }

      queryParams.forEach(param => {
        if (param.key && param.value) {
          let parsedValue: any = param.value

          if (param.type === 'number') {
            parsedValue = Number(param.value)
          } else if (param.type === 'boolean') {
            parsedValue = param.value.toLowerCase() === 'true'
          }

          queryObject[param.key] = parsedValue
        }
      })

      const result = await fetchMongoDBData(selectedCollection, queryObject)

      if (result && typeof result === 'object') {
        if (Array.isArray(result)) {
          setData(result)
        } else {
          setData([result])
        }
      } else {
        setData([])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取数据失败')
      setData(null)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Stack className="w-full max-w-5xl mx-auto p-4">
      <Paper p="lg" shadow="sm" className="w-full">
        <Title order={3} className="mb-4">MongoDB 高级数据查询</Title>

        {isLoadingCollections ? (
          <Box className="flex justify-center p-4">
            <Loader size="sm" />
            <Text ml="xs">加载集合列表...</Text>
          </Box>
        ) : (
          <Select
            label="选择集合"
            placeholder="选择一个集合"
            data={collections.map(col => ({ value: col, label: col }))}
            value={selectedCollection}
            onChange={(value) => {
              setSelectedCollection(value || '')
              setData(null)
            }}
            className="mb-4"
            searchable
            clearable
          />
        )}

        <Accordion className="mb-4">
          <Accordion.Item value="queryParams">
            <Accordion.Control>
              <Group>
                <Text>查询参数</Text>
                <Badge>{queryParams.filter(p => p.key && p.value).length}</Badge>
                <Button onClick={addQueryParam} variant="outline">添加参数</Button>
              </Group>
            </Accordion.Control>
            <Accordion.Panel>
              {queryParams.map((param, index) => (
                <Group key={index} className="mb-2" align="flex-end">
                  <Select
                    label="参数名"
                    placeholder="选择或输入参数名"
                    data={availableFields.map(field => ({ value: field, label: field }))}
                    value={param.key}
                    onChange={(value) => updateQueryParam(index, 'key', value || '')}
                    className="flex-1"
                    searchable
                  />

                  <Select
                    label="类型"
                    value={param.type}
                    onChange={(value) => updateQueryParam(index, 'type', value || 'string')}
                    data={[
                      { value: 'string', label: '字符串' },
                      { value: 'number', label: '数字' },
                      { value: 'boolean', label: '布尔值' }
                    ]}
                    className="w-24"
                  />

                  {param.type === 'string' && (
                    <TextInput
                      label="参数值"
                      placeholder="输入值"
                      value={param.value}
                      onChange={(e) => updateQueryParam(index, 'value', e.target.value)}
                      className="flex-1"
                    />
                  )}

                  {param.type === 'number' && (
                    <NumberInput
                      label="参数值"
                      placeholder="输入数字"
                      value={Number(param.value) || 0}
                      onChange={(val) => updateQueryParam(index, 'value', String(val))}
                      className="flex-1"
                    />
                  )}

                  {param.type === 'boolean' && (
                    <Box className="flex-1">
                      <Text size="sm" className="mb-1">参数值</Text>
                      <Switch
                        label={param.value === 'true' ? '是' : '否'}
                        checked={param.value === 'true'}
                        onChange={(e) => updateQueryParam(index, 'value', e.currentTarget.checked ? 'true' : 'false')}
                      />
                    </Box>
                  )}

                  <Button
                    color="red"
                    onClick={() => removeQueryParam(index)}
                    disabled={queryParams.length === 1}
                  >
                    删除
                  </Button>
                </Group>
              ))}
            </Accordion.Panel>
          </Accordion.Item>
        </Accordion>

        <Button
          onClick={fetchData}
          color="blue"
          className="w-full"
          loading={isLoading}
          disabled={!selectedCollection}
        >
          查询数据
        </Button>
      </Paper>

      {error && (
        <Paper p="md" className="bg-red-50 text-red-700">
          <Text>{error}</Text>
        </Paper>
      )}
     
      {data && (
        <JSONTree newJSON={JSON.stringify(data, null, 2)} />
      )}
    </Stack>
  )
}

export default AdvancedMongoDBFetcher
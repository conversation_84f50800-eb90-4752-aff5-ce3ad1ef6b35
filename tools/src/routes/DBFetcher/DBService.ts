import { wrapFetch } from '@/APIs'

export interface MongoDBQueryParams {
  [key: string]: string | number | boolean
}

/**
 * 从MongoDB获取数据
 * @param collection 集合名称
 * @param params 查询参数
 * @returns 查询结果
 */
export const fetchMongoDBData = async (collection: string, params?: MongoDBQueryParams) => {
  const [err, res] = await wrapFetch<{ data: unknown[] }>('mongoData', {
    requestData: {
      collection,
      params
    }
  })

  if (err || res?.code !== 0) return []

  return res.data
}

/**
 * 获取可用的MongoDB集合列表
 * @returns 集合列表
 */
export const fetchCollections = async () => {
  const [err, res] = await wrapFetch<{ data: string[] }>('mongoCollections', {})

  if (err || res?.code !== 0) return []

  return res.data
}

/**
 * 获取集合的字段信息
 * @param collection 集合名称
 * @returns 字段信息
 */
export const fetchCollectionSchema = async (collection: string) => {
  const [err, res] = await wrapFetch<{ data: unknown[] }>('mongoSchema', {
    requestData: {
      collection
    }
  })

  if (err || res?.code !== 0) return []

  return res.data
}
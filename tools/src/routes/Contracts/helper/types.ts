export interface BaseItemPriceConfig {
  orderConsumerId: string
  itemIds: string[]
  type: 'coefficient' | 'number'
  // 系数定价模式下的字段
  displayBase?: 'marketPrice' | 'displayPrice' | 'salePrice' | 'settlementPrice'
  displayCoefficient?: number
  saleBase?: 'marketPrice' | 'displayPrice' | 'salePrice' | 'settlementPrice'
  saleCoefficient?: number
  settlementBase?: 'marketPrice' | 'displayPrice' | 'salePrice' | 'settlementPrice'
  settlementCoefficient?: number
  // 固定价格模式下的字段
  displayPrice?: number
  salePrice?: number
  settlementPrice?: number
}


export enum SettleMethodEnum {
  Unified = '统一结算',
  SelfPay = 'C端自付',
  Both = '统一结算+C端自付',
}

export interface RequiredRule {
  type: 'required'
  data: {
    baseItemIds: string[]
    itemIds: string[]
  }
}

export interface BundledRule {
  type: 'bundled'
  data: string[]
}

export interface RemoveRedundantItemsRule {
  type: 'removeRedundantItems'
  data: Array<{
    id: string
    isRedundantItem?: boolean
  }>
}

export interface IgnorableBaseItemsRule {
  type: 'ignorableBaseItems'
  data: Array<{
    baseItemId: string
    needFeedback: boolean
  }>
}

export interface ZeroNeedOneRule {
  type: '0Need1'
  data: [string[], string[]]
}

export type ContractRule = 
  | RequiredRule 
  | BundledRule 
  | RemoveRedundantItemsRule 
  | IgnorableBaseItemsRule 
  | ZeroNeedOneRule

export interface ServerContract {
  beginDate: string
  _id: string
  contractId: string
  endDate: string
  orderProducerId: string // 甲方代号，如icbcaxa-sh
  orderProducerName: string // 甲方名称
  baseItemPriceConfig: BaseItemPriceConfig[]
  contactMobile: string
  contactName: string
  internalContact: string
  internalContactMobile: string
  isUnderwriting: boolean
  name: string
  rules: ContractRule[]
  settleMethod: SettleMethodEnum
  settlementPeriod: 'monthly' | 'quarterly' | 'yearly'
  paymentEntity: 'haola' | 'partner'
  updateDate: string
}




export type Contract = Omit<ServerContract, 'endDate' | 'beginDate' | 'baseItemPriceConfig'> & {
  validPeriod: [Date, Date]
  priceConfig: BaseItemPriceConfig[]
  // partnerId: string
}

export interface PartnerItem {
  _id?: string
  itemId: string
  orderConsumerId: string
  baseItems: { id: string }[]
  beginDate: Date
  displayPrice: string
  endDate: Date
  name: string
  sellingPrice: string
  settlementPrice: string
}

export type ContractWithPartner = Contract & { partnerId: string }

export interface Vendor {
  orderConsumerId: string
  name: string
  parents: string[]
}

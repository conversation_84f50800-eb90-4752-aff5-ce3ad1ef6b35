import { ContractRule, RequiredRule, BundledRule, RemoveRedundantItemsRule, IgnorableBaseItemsRule, ZeroNeedOneRule } from './types'

/**
 * 检查项目是否满足必选规则
 * @param selectedItems 已选择的项目ID列表
 * @param rule 必选规则
 * @returns 需要添加的项目ID列表
 */
export const checkRequiredRule = (selectedItems: string[], rule: RequiredRule): string[] => {
  const { baseItemIds, itemIds } = rule.data
  // 如果已选择的项目中没有包含必选项目，则返回需要添加的项目
  const missingItems = baseItemIds.filter(id => !selectedItems.includes(id))
  return missingItems.length > 0 ? itemIds : []
}

/**
 * 检查项目是否满足绑定规则
 * @param selectedItems 已选择的项目ID列表
 * @param rule 绑定规则
 * @returns 需要添加的项目ID列表
 */
export const checkBundledRule = (selectedItems: string[], rule: BundledRule): string[] => {
  const { data: bundledItems } = rule
  // 如果已选择的项目中包含任意一个绑定项目，则检查其他绑定项目是否都已选择
  const hasAnyBundledItem = bundledItems.some(id => selectedItems.includes(id))
  if (!hasAnyBundledItem) return []
  
  return bundledItems.filter(id => !selectedItems.includes(id))
}

/**
 * 检查项目是否满足冗余项目规则
 * @param selectedItems 已选择的项目ID列表
 * @param rule 冗余项目规则
 * @returns 需要移除的项目ID列表
 */
export const checkRemoveRedundantItemsRule = (selectedItems: string[], rule: RemoveRedundantItemsRule): string[] => {
  const { data } = rule
  return data
    .filter(item => item.isRedundantItem && selectedItems.includes(item.id))
    .map(item => item.id)
}

/**
 * 检查项目是否满足可忽略最小项规则
 * @param selectedItems 已选择的项目ID列表
 * @param rule 可忽略最小项规则
 * @returns { missingItems: string[], needFeedback: boolean } 缺失的项目和是否需要反馈
 */
export const checkIgnorableBaseItemsRule = (
  selectedItems: string[], 
  rule: IgnorableBaseItemsRule
): { missingItems: string[], needFeedback: boolean } => {
  const { data } = rule
  const missingItems = data
    .filter(item => !selectedItems.includes(item.baseItemId))
    .map(item => item.baseItemId)
  
  const needFeedback = data.some(item => 
    !selectedItems.includes(item.baseItemId) && item.needFeedback
  )

  return { missingItems, needFeedback }
}

/**
 * 检查项目是否满足依赖规则
 * @param selectedItems 已选择的项目ID列表
 * @param rule 依赖规则
 * @returns 需要添加的项目ID列表
 */
export const checkZeroNeedOneRule = (selectedItems: string[], rule: ZeroNeedOneRule): string[] => {
  const [dependentItems, requiredItems] = rule.data
  
  // 检查是否选择了依赖项目
  const hasDependentItem = dependentItems.some(id => selectedItems.includes(id))
  if (!hasDependentItem) return []
  
  // 检查是否至少选择了一个被依赖项目
  const hasRequiredItem = requiredItems.some(id => selectedItems.includes(id))
  if (hasRequiredItem) return []
  
  // 如果没有选择任何被依赖项目，则返回第一个被依赖项目
  return [requiredItems[0]]
}

/**
 * 应用所有规则并返回需要添加和移除的项目
 */
export const applyContractRules = (
  selectedItems: string[], 
  rules: ContractRule[]
): { 
  itemsToAdd: string[], 
  itemsToRemove: string[],
  missingItems: string[],
  needFeedback: boolean 
} => {
  const itemsToAdd: string[] = []
  const itemsToRemove: string[] = []
  let missingItems: string[] = []
  let needFeedback = false

  rules.forEach(rule => {
    let result: { missingItems: string[], needFeedback: boolean }
    
    switch (rule.type) {
      case 'required':
        itemsToAdd.push(...checkRequiredRule(selectedItems, rule))
        break
      case 'bundled':
        itemsToAdd.push(...checkBundledRule(selectedItems, rule))
        break
      case 'removeRedundantItems':
        itemsToRemove.push(...checkRemoveRedundantItemsRule(selectedItems, rule))
        break
      case 'ignorableBaseItems':
        result = checkIgnorableBaseItemsRule(selectedItems, rule)
        missingItems = [...missingItems, ...result.missingItems]
        needFeedback = needFeedback || result.needFeedback
        break
      case '0Need1':
        itemsToAdd.push(...checkZeroNeedOneRule(selectedItems, rule))
        break
    }
  })

  return {
    itemsToAdd: [...new Set(itemsToAdd)],
    itemsToRemove: [...new Set(itemsToRemove)],
    missingItems: [...new Set(missingItems)],
    needFeedback
  }
} 
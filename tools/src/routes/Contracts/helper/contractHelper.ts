import { Contract, ServerContract } from './types'

/**
 * 将 ServerContract 转换为 Contract 类型（ISO字符串转Date数组）
 */
export function serverContractToContract(
  serverContract: ServerContract
): Contract {
  return {
    ...omit(serverContract, ['beginDate', 'endDate', 'baseItemPriceConfig']),
    validPeriod: [
      new Date(serverContract.beginDate),
      new Date(serverContract.endDate),
    ],
    priceConfig: serverContract.baseItemPriceConfig,
  }
}

/**
 * 将 Contract 转换为 ServerContract 类型（Date数组转ISO字符串）
 */
export function contractToServerContract(contract: Contract): ServerContract {
  const {validPeriod, priceConfig, ...serverContract} = contract
  return {
    ...serverContract,
    beginDate: validPeriod[0].toISOString(),
    endDate: validPeriod[1].toISOString(),
    baseItemPriceConfig: priceConfig,
  }
}

// 辅助函数：实现类似lodash的omit功能
function omit<T extends object, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj }
  keys.forEach((key) => delete result[key])
  return result
}

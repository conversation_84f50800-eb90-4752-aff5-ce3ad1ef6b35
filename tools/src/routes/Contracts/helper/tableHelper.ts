import { Contract } from "./types"
import { formatDate } from '@/utils/share'

const PAYMENT_SUBJECT_MAP: Record<string, string>  = {
  haola: '好啦',
  partner: '合作方',
}
const SETTLEMENT_PERIOD_MAP: Record<string, string> = {
  monthly: '月结',
  quarterly: '季结',
  yearly: '年结',
}

export const TABLE_HEADERS: {key: keyof Contract, label: string, maxWidth?: number, ellipse?: boolean, formatter?: (contract: Contract) => string}[] = [
  { key: 'orderProducerId', label: '甲方id' },
  { key: 'orderProducerName', label: '甲方名称' },
  { key: 'name', label: '合同名称', maxWidth: 200, ellipse: true },
  { key: 'contractId', label: '合同号' },
  { 
    key: 'validPeriod', 
    label: '合同有效期', 
    formatter: contract => `${formatDate(contract.validPeriod[0])} ~ ${formatDate(contract.validPeriod[1])}`
  },
  { key: 'settleMethod', label: '结算方式', formatter: contract => contract.settleMethod || '' },
  { key: 'settlementPeriod', label: '结算周期', formatter: contract => SETTLEMENT_PERIOD_MAP[contract.settlementPeriod] || ''  },
  { key: 'paymentEntity', label: '支付主体', formatter: contract => PAYMENT_SUBJECT_MAP[contract.paymentEntity] || ''  },
  { key: 'isUnderwriting', label: '配置方式', formatter: contract => contract.isUnderwriting ? '核保体检' : '常规体检' },
  { key: 'updateDate', label: '更新时间', formatter: contract => formatDate(contract.updateDate, 'YYYY/MM/DD HH:mm') },
]

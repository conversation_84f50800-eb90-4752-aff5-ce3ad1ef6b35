import { DataTable, DataTableColumn } from 'mantine-datatable'
import { Box, Text, Tooltip, Table } from '@mantine/core'
import { ReactNode, useState } from 'react'

import { Contract, ContractRule } from '../helper/types'
import { TABLE_HEADERS } from '../helper/tableHelper'

import Empty from '@/components/icons/Empty'

import tableStyles from '../css/table.module.css'

interface ContractTableProps {
  contracts: Contract[]
  actions?: DataTableColumn[]
}

const RULE_TYPE_LABEL_MAP: Record<string, string> = {
  required: '必选规则',
  bundled: '绑定规则',
  removeRedundantItems: '冗余项目规则',
  ignorableBaseItems: '可忽略最小项规则',
  '0Need1': '依赖规则',
}

const RULE_CONTENT_RENDERERS = {
  required: (data: Extract<ContractRule, { type: 'required' }>['data']) => (
    <>
      <Text size="sm">最小项: {data.baseItemIds.join(', ')}</Text>
      <Text size="sm">项目: {data.itemIds.join(', ')}</Text>
    </>
  ),
  bundled: (data: Extract<ContractRule, { type: 'bundled' }>['data']) => (
    <Text size="sm">绑定项目: {data.join(', ')}</Text>
  ),
  removeRedundantItems: (data: Extract<ContractRule, { type: 'removeRedundantItems' }>['data']) => (
    <Text size="sm">冗余项目: {data.map(item => item.id).join(', ')}</Text>
  ),
  ignorableBaseItems: (data: Extract<ContractRule, { type: 'ignorableBaseItems' }>['data']) => (
    <Text size="sm">可忽略最小项: {data.map(item => item.baseItemId).join(', ')}</Text>
  ),
  '0Need1': (data: Extract<ContractRule, { type: '0Need1' }>['data']) => (
    <>
      <Text size="sm">依赖项: {data[0].join(', ')}</Text>
      <Text size="sm">被依赖项: {data[1].join(', ')}</Text>
    </>
  ),
} as const

function renderRuleContent(rule: ContractRule) {
  // return RULE_CONTENT_RENDERERS[rule.type]?.(rule.data)

  switch (rule.type) {
    case 'required':
      return RULE_CONTENT_RENDERERS.required(rule.data)
    case 'bundled':
      return RULE_CONTENT_RENDERERS.bundled(rule.data)
    case 'removeRedundantItems':
      return RULE_CONTENT_RENDERERS.removeRedundantItems(rule.data)
    case 'ignorableBaseItems':
      return RULE_CONTENT_RENDERERS.ignorableBaseItems(rule.data)
    case '0Need1':
      return RULE_CONTENT_RENDERERS['0Need1'](rule.data)
    default:
      return null
  }
}

const renderPrice = (config: Contract['priceConfig'][0]) => {
  const { type, displayBase, displayCoefficient, saleBase, saleCoefficient, settlementBase, settlementCoefficient, displayPrice, salePrice, settlementPrice } = config
  return type === 'coefficient'
    ? {
      type: '系数定价',
      display: `${displayBase} × ${displayCoefficient}`,
      sale: `${saleBase} × ${saleCoefficient}`,
      settlement: `${settlementBase} × ${settlementCoefficient}`
    }
    : {
      type: '固定价格',
      display: displayPrice,
      sale: salePrice,
      settlement: settlementPrice
    }
}

export default function ContractTable({ contracts, actions }: ContractTableProps) {
  const [expanded, setExpanded] = useState<string[]>([])

  const columns: DataTableColumn<Contract>[] = TABLE_HEADERS.map(header => ({
    accessor: header.key,
    title: header.label,
    width: header.maxWidth,
    ellipsis: header.ellipse,
    render: (contract: Contract): ReactNode => {
      if (header.formatter) {
        return header.formatter(contract)
      }

      const v = contract[header.key] ?? ''
      const value = `${v}`
      if (header.ellipse) {
        return (
          <Tooltip label={value}>
            <Text truncate="end" style={{ maxWidth: header.maxWidth }}>
              {value}
            </Text>
          </Tooltip>
        )
      }
      return value
    }
  }))

  const expandContent = ({ record }: { record: Contract }) => (
    <Box p="md">
      {record.priceConfig?.length > 0 && (
        <Box mb="md">
          <Text fw={500} mb="xs">价格配置</Text>
          <Table striped withTableBorder withColumnBorders classNames={{ ...tableStyles }}>
            <Table.Thead>
              <Table.Tr className='!bg-[#a4bfc2b3] text-white'>
                <Table.Th>乙方</Table.Th>
                <Table.Th>定价模式</Table.Th>
                <Table.Th>展示价</Table.Th>
                <Table.Th>销售价</Table.Th>
                <Table.Th>结算价</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {record.priceConfig.map((config, index) => {
                const prices = renderPrice(config)
                return (
                  <Table.Tr key={index}>
                    <Table.Td>{config.orderConsumerId}</Table.Td>
                    <Table.Td>{prices.type}</Table.Td>
                    <Table.Td>{prices.display}</Table.Td>
                    <Table.Td>{prices.sale}</Table.Td>
                    <Table.Td>{prices.settlement}</Table.Td>
                  </Table.Tr>
                )
              })}
            </Table.Tbody>
          </Table>
        </Box>
      )}

      {record.rules?.length > 0 && (
        <Box>
          <Text fw={500} mb="xs">规则配置</Text>
          {record.rules?.map((rule, index) => (
            <Box key={index} p="xs" style={{ border: '1px solid #eee', borderRadius: '4px' }} mb="xs">
              <Text size="sm">规则类型: {RULE_TYPE_LABEL_MAP[rule.type] || rule.type}</Text>
              {renderRuleContent(rule)}
            </Box>
          ))}
        </Box>
      )}

    </Box>
  )

  return (
    <DataTable
      withTableBorder={false}
      withRowBorders
      borderRadius="sm"
      highlightOnHover
      striped
      columns={[...columns, ...actions ?? []]}
      minHeight={150}
      noRecordsIcon={
        <Box p={4} mb={4}>
          <Empty />
        </Box>
      }
      noRecordsText="暂无数据"
      records={contracts}
      idAccessor="contractId"
      tabularNums
      rowExpansion={{
        expanded: { recordIds: expanded, onRecordIdsChange: setExpanded },
        content: expandContent,
        expandable: ({ record: { rules, priceConfig } }) => rules.length > 0 || priceConfig.length > 0
      }}
    />
  )
} 
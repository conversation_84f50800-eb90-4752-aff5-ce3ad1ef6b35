import { Box, Group, Select, Button, Title, Stack } from '@mantine/core'
import { UseFormReturnType } from '@mantine/form'
import { PlusIcon, TrashIcon } from '@radix-ui/react-icons'
import { ContractRule, ContractWithPartner, RequiredRule, BundledRule, RemoveRedundantItemsRule, IgnorableBaseItemsRule, ZeroNeedOneRule } from '../helper/types'

import ItemsSelector from './ItemsSelector'

import formStyles from '../css/form.module.css'

interface RulesConfigProps {
  form: UseFormReturnType<ContractWithPartner>
}

const RULE_TYPES = [
  { value: 'required', label: '必选规则' },
  { value: 'bundled', label: '绑定规则' },
  { value: 'removeRedundantItems', label: '冗余项目规则' },
  { value: 'ignorableBaseItems', label: '可忽略最小项规则' },
  { value: '0Need1', label: '依赖规则' },
] as const

const defaultRule: RequiredRule = {
  type: 'required',
  data: {
    baseItemIds: [],
    itemIds: [],
  },
}

export default function RulesConfig({ form }: RulesConfigProps) {
  const addRule = () => {
    form.setFieldValue('rules', [...form.values.rules, { ...defaultRule }])
  }

  const removeRule = (index: number) => {
    form.setFieldValue(
      'rules',
      form.values.rules.filter((_, i) => i !== index)
    )
  }

  const renderRequiredRule = (index: number) => {
    const rule = form.values.rules[index] as RequiredRule
    return (
      <>
        <ItemsSelector
          label="最小项"
          className='flex items-center'
          classNames={{ ...formStyles }}
          selectedItems={rule.data.baseItemIds}
          onSelect={val => form.setFieldValue(`rules.${index}.data.baseItemIds`, val)}
        />
        <ItemsSelector
          label="项目"
          className='flex items-center'
          classNames={{ ...formStyles }}
          selectedItems={rule.data.itemIds}
          onSelect={val => form.setFieldValue(`rules.${index}.data.itemIds`, val)}
        />
      </>
    )
  }

  const renderBundledRule = (index: number) => {
    const rule = form.values.rules[index] as BundledRule
    return (
      <ItemsSelector
        label="绑定项目"
        className='flex items-center'
        classNames={{ ...formStyles }}
        selectedItems={rule.data}
        onSelect={val => form.setFieldValue(`rules.${index}.data`, val)}
      />
    )
  }

  const renderRemoveRedundantItemsRule = (index: number) => {
    const rule = form.values.rules[index] as RemoveRedundantItemsRule
    return (
      <ItemsSelector
        label="冗余项目"
        className='flex items-center'
        classNames={{ ...formStyles }}
        selectedItems={rule.data.map(item => item.id)}
        onSelect={val => {
          const items = val.map(id => ({
            id,
            isRedundantItem: rule.data.find(item => item.id === id)?.isRedundantItem ?? false
          }))
          form.setFieldValue(`rules.${index}.data`, items)
        }}
      />
    )
  }

  const renderIgnorableBaseItemsRule = (index: number) => {
    const rule = form.values.rules[index] as IgnorableBaseItemsRule
    return (
      <ItemsSelector
        label="可忽略最小项"
        className='flex items-center'
        classNames={{ ...formStyles }}
        selectedItems={rule.data.map(item => item.baseItemId)}
        onSelect={val => {
          const items = val.map(baseItemId => ({
            baseItemId,
            needFeedback: rule.data.find(item => item.baseItemId === baseItemId)?.needFeedback ?? false
          }))
          form.setFieldValue(`rules.${index}.data`, items)
        }}
      />
    )
  }

  const renderZeroNeedOneRule = (index: number) => {
    const rule = form.values.rules[index] as ZeroNeedOneRule
    return (
      <>
        <ItemsSelector
          label="依赖项目"
          className='flex items-center'
          classNames={{ ...formStyles }}
          selectedItems={rule.data[0]}
          onSelect={val => form.setFieldValue(`rules.${index}.data.0`, val)}
        />
        <ItemsSelector
          label="被依赖项目"
          className='flex items-center'
          classNames={{ ...formStyles }}
          selectedItems={rule.data[1]}
          onSelect={val => form.setFieldValue(`rules.${index}.data.1`, val)}
        />
      </>
    )
  }

  const renderRuleConfig = (rule: ContractRule, index: number) => {
    const map = {
      required: renderRequiredRule,
      bundled: renderBundledRule,
      removeRedundantItems: renderRemoveRedundantItemsRule,
      ignorableBaseItems: renderIgnorableBaseItemsRule,
      '0Need1': renderZeroNeedOneRule,
    }
    return map[rule.type](index)
  }

  return (
    <Box my='lg'>
      <Stack gap="lg">
        {form.values.rules?.map((rule, index) => (
          <Box key={index} p="md" style={{ border: '1px solid #eee', borderRadius: '4px' }}>
            <Group justify="space-between" mb="md">
              <Title order={4}>规则配置 {index + 1}</Title>
              <Button
                variant="subtle"
                color="red"
                leftSection={<TrashIcon />}
                onClick={() => removeRule(index)}
              >
                删除
              </Button>
            </Group>

            <Select
              label="规则类型"
              withAsterisk
              data={RULE_TYPES}
              className='flex items-center'
              classNames={{ ...formStyles }}
              value={rule.type}
              onChange={(value) => {
                let newRule: ContractRule
                switch (value) {
                  case 'required':
                    newRule = {
                      type: 'required',
                      data: {
                        baseItemIds: [],
                        itemIds: [],
                      },
                    }
                    break
                  case 'bundled':
                    newRule = {
                      type: 'bundled',
                      data: [],
                    }
                    break
                  case 'removeRedundantItems':
                    newRule = {
                      type: 'removeRedundantItems',
                      data: [],
                    }
                    break
                  case 'ignorableBaseItems':
                    newRule = {
                      type: 'ignorableBaseItems',
                      data: [],
                    }
                    break
                  case '0Need1':
                    newRule = {
                      type: '0Need1',
                      data: [[], []],
                    }
                    break
                  default:
                    return
                }
                form.setFieldValue(`rules.${index}`, newRule)
              }}
            />

            {renderRuleConfig(rule, index)}
          </Box>
        ))}

        <Button
          variant="light"
          leftSection={<PlusIcon />}
          onClick={addRule}
        >
          添加规则配置
        </Button>
      </Stack>
    </Box>
  )
} 
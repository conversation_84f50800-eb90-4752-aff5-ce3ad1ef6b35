import { Button, Group, NumberInput, Stack, TextInput, Table, ActionIcon, Center, ScrollArea, Modal, Tooltip, Text, LoadingOverlay } from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import { PlusIcon, Pencil2Icon } from '@radix-ui/react-icons'

import Empty from '@/components/icons/Empty'
import ItemsSelector from './ItemsSelector'

import { usePartnerItem } from '../hooks/usePartnerItem'
import { handleStringArray } from '@/utils/share'

import formStyles from '../css/form.module.css'
import modalStyles from '../css/index.module.css'

interface PartnerItemFormProps {
  contractId: string
  close: () => void
}

export default function PartnerItemForm({ contractId }: PartnerItemFormProps) {
  const {
    form,
    editingId,
    partnerItems,
    opened,
    close,
    addPartnerItem,
    handleEdit,
    loading,
    onsubmit
  } = usePartnerItem(contractId)

  const renderTableRows = () =>
    partnerItems.map((item, index) => (
      <Table.Tr key={index}>
        <Table.Td>{item.itemId}</Table.Td>
        <Table.Td>{item.name}</Table.Td>
        <Table.Td>
          <Tooltip label={item.baseItems.map(bi => bi.id).join(',')}>
            <Text style={{ width: '100%', display: 'block', fontSize: '12px' }}>
              {handleStringArray(item.baseItems.map(bi => bi.id))}
            </Text>
          </Tooltip>
        </Table.Td>
        <Table.Td>{item.displayPrice}</Table.Td>
        <Table.Td>{item.sellingPrice}</Table.Td>
        <Table.Td>{item.settlementPrice}</Table.Td>
        <Table.Td>
          <Group>
            <ActionIcon color="cyan" onClick={() => handleEdit(index)}>
              <Pencil2Icon />
            </ActionIcon>
          </Group>
        </Table.Td>
      </Table.Tr>
    ))

  const renderNumberInput = (label: string, field: string) => (
    <NumberInput
      label={label}
      placeholder={`请输入${label}`}
      min={0}
      decimalScale={2}
      className='flex items-center'
      classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
      value={parseFloat(form.values.partnerItem[field])}
      onChange={val => form.setFieldValue(`partnerItem.${field}`, val?.toString() ?? '')}
    />
  )

  return (
    <Stack gap="md">
      <LoadingOverlay visible={loading} zIndex={1000} overlayProps={{ radius: "sm", blur: 2 }} loaderProps={{ color: 'cyan', type: 'bars' }}/>
      <Table.ScrollContainer minWidth={800} maxHeight='calc(100vh - 240px)'>
        <Table striped tabularNums style={{ tableLayout: 'fixed', fontSize: '13px' }}>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>项目ID</Table.Th>
              <Table.Th>项目名称</Table.Th>
              <Table.Th className='w-48'>好啦最小项</Table.Th>
              <Table.Th>展示价</Table.Th>
              <Table.Th>销售价</Table.Th>
              <Table.Th>结算价</Table.Th>
              <Table.Th className='w-16'>操作</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {renderTableRows()}
          </Table.Tbody>
        </Table>
      </Table.ScrollContainer>
      {!partnerItems.length && <Center>
        <Empty />
      </Center>}

      <Button
        variant="light"
        leftSection={<PlusIcon />}
        onClick={addPartnerItem}
      >
        添加甲方项目
      </Button>

      <Modal
        opened={opened}
        onClose={() => handleEdit(-1)}
        title={`${editingId ? '编辑' : '新增'}甲方项目`}
        size="70%"
        transitionProps={{ transition: 'fade', duration: 100, timingFunction: 'linear' }}
        classNames={{ content: modalStyles.modalContent, title: modalStyles.modalTitle }}
      >
        <form onSubmit={onsubmit}>
          <ScrollArea h={450}>
            <Stack gap="md">
              <TextInput
                label="项目ID"
                placeholder="请输入项目ID"
                className='flex items-center'
                classNames={{ ...formStyles }}
                {...form.getInputProps('partnerItem.itemId')}
              />

              <TextInput
                label="项目名称"
                placeholder="请输入项目名称"
                className='flex items-center'
                classNames={{ ...formStyles }}
                {...form.getInputProps('partnerItem.name')}
              />

              <ItemsSelector
                label="好啦最小项"
                className='flex items-center'
                classNames={{ ...formStyles }}
                selectedItems={form.values.partnerItem.baseItems.map(bi => bi.id)}
                onSelect={val => form.setFieldValue('partnerItem.baseItems', val.map(id => ({ id })))}
              />

              <DatePickerInput
                className='flex items-center'
                classNames={{ ...formStyles }}
                label="有效期"
                placeholder="请选择有效期"
                type="range"
                withAsterisk
                clearable
                valueFormat='YYYY/MM/DD'
                firstDayOfWeek={0}
                {...form.getInputProps('partnerItem.validPeriod')}
              />

              <div className='grid grid-cols-2 gap-x-4 gap-y-1'>
                {renderNumberInput('展示价', 'displayPrice')}
                {renderNumberInput('销售价', 'sellingPrice')}
                {renderNumberInput('结算价', 'settlementPrice')}
              </div>
            </Stack>
          </ScrollArea>
          <Group justify="flex-end" mt="md">
            <Button variant="light" onClick={close}>关闭</Button>
            <Button type='submit'>保存</Button>
          </Group>
        </form>
      </Modal>
    </Stack>
  )
}
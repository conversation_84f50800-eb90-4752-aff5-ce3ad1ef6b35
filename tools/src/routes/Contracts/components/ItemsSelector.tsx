import { useState, useEffect, useMemo, useCallback } from 'react'
import { useAtom } from 'jotai'
import {
  Modal,
  Input,
  Text,
  Group,
  Stack,
  Checkbox,
  ScrollArea,
  Box,
  Button,
} from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import { useDebouncedValue } from '@mantine/hooks'
import InputModal from '@/components/Modals/InputModal'
import { hlItemsAtom, categoriesAtom, selectedCategoryCountsAtom } from '@/store/checkItems'
import { handleStringArray } from '@/utils/share'

import modalStyles from '../css/index.module.css'
import { HLItem } from '@/store/types'

interface Item {
  id: string
  category: string
  name: string
}

interface ItemsSelectorProps {
  label?: string
  placeholder?: string
  className?: string
  classNames?: Record<string, string>
  onSelect: (selectedItems: string[]) => void
  selectedItems?: string[]
}

// 抽离分类列表组件
const CategoryList = ({ 
  categories, 
  selectedCategory, 
  categoryCounts, 
  onSelect 
}: { 
  categories: string[]
  selectedCategory: string
  categoryCounts: Record<string, number>
  onSelect: (category: string) => void 
}) => (
  <Box className='border-r border-gray-200'>
    <ScrollArea h={400}>
      <Stack gap="xs">
        {categories.map((category) => (
          <Group
            key={category}
            justify="space-between"
            style={{
              backgroundColor: selectedCategory === category ? 'var(--mantine-color-cyan-0)' : 'transparent',
              borderRadius: '4px',
            }}
            className='pl-2 py-2 pr-4 cursor-pointer'
            onClick={() => onSelect(category)}
          >
            <Text>{category}</Text>
            {categoryCounts[category] > 0 && (
              <Text size="sm" c="dimmed" fw={500}>
                {categoryCounts[category]}
              </Text>
            )}
          </Group>
        ))}
      </Stack>
    </ScrollArea>
  </Box>
)

// 抽离项目列表组件
const ItemsList = ({ 
  items, 
  selectedItems, 
  onItemSelect 
}: { 
  items: HLItem[]
  selectedItems: string[]
  onItemSelect: (item: Item, checked: boolean) => void 
}) => (
  <Box className='col-span-2 pl-5'>
    <ScrollArea h={400}>
      <Stack gap="xs">
        {items.map((item) => (
          <Checkbox
            key={item.id}
            label={item.name}
            description={item.id}
            checked={selectedItems.includes(item.id)}
            onChange={(e) => onItemSelect(item, e.currentTarget.checked)}
          />
        ))}
      </Stack>
    </ScrollArea>
  </Box>
)

export default function ItemsSelector({ label, onSelect, selectedItems = [], ...props }: ItemsSelectorProps) {
  const [opened, { open, close }] = useDisclosure(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearch] = useDebouncedValue(searchQuery, 200)
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [localSelectedItems, setLocalSelectedItems] = useState<string[]>(selectedItems)

  const [hlItems] = useAtom(hlItemsAtom)
  const [categories] = useAtom(categoriesAtom)
  const getSelectedCategoryCounts = useAtom(selectedCategoryCountsAtom)[0]

  // 默认展开第一个分类
  useEffect(() => {
    if (categories.length > 0 && !selectedCategory) {
      setSelectedCategory(categories[0])
    }
  }, [categories, selectedCategory])

  // 当前分类检查项
  const filteredItems = useMemo(() => 
    hlItems.filter(item =>
      item.name.toLowerCase().includes(debouncedSearch.toLowerCase())
    ),
    [hlItems, debouncedSearch]
  )

  const currentCategoryItems = useMemo(() => 
    hlItems.filter(item => item.category === selectedCategory),
    [hlItems, selectedCategory]
  )

  const categoryCounts = useMemo(() => 
    getSelectedCategoryCounts(localSelectedItems),
    [getSelectedCategoryCounts, localSelectedItems]
  )

  const isCurrentCategoryAllSelected = useMemo(() => 
    currentCategoryItems.every(item => localSelectedItems.includes(item.id)),
    [currentCategoryItems, localSelectedItems]
  )

  useEffect(() => {
    if (debouncedSearch && filteredItems.length > 0) {
      setSelectedCategory(filteredItems[0].category)
    }
  }, [debouncedSearch, filteredItems])

  const handleItemSelect = useCallback((item: Item, checked: boolean) => {
    setLocalSelectedItems(prev => 
      checked 
        ? [...prev, item.id]
        : prev.filter(i => i !== item.id)
    )
  }, [])

  const handleConfirm = useCallback(() => {
    onSelect(localSelectedItems)
    close()
  }, [localSelectedItems, onSelect, close])

  const handleSelectAll = useCallback(() => {
    const currentCategoryItemIds = currentCategoryItems.map(item => item.id)
    const otherCategoryItems = localSelectedItems.filter(id => !currentCategoryItemIds.includes(id))
    
    setLocalSelectedItems(
      isCurrentCategoryAllSelected
        ? otherCategoryItems
        : [...otherCategoryItems, ...currentCategoryItemIds]
    )
  }, [currentCategoryItems, localSelectedItems, isCurrentCategoryAllSelected])

  return (
    <>
      <InputModal 
        value={handleStringArray(selectedItems)} 
        label={label} 
        onFocus={open} 
        withAsterisk 
        {...props} 
      />
      <Modal
        opened={opened}
        onClose={close}
        title="选择检查项"
        size="75%"
        classNames={{ content: modalStyles.modalContent, title: modalStyles.modalTitle }}
      >
        <Stack gap="md">
          <div className='flex items-center justify-between gap-2'>
            <Input
              placeholder="Search hlItems..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftSection="🔍"
            />
            <Checkbox 
              label="全选" 
              checked={isCurrentCategoryAllSelected} 
              onChange={handleSelectAll} 
            />
          </div>

          <div className='grid grid-cols-3 gap-4'>
            <CategoryList
              categories={categories}
              selectedCategory={selectedCategory}
              categoryCounts={categoryCounts}
              onSelect={setSelectedCategory}
            />
            <ItemsList
              items={currentCategoryItems}
              selectedItems={localSelectedItems}
              onItemSelect={handleItemSelect}
            />
          </div>

          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              已选: {localSelectedItems.length} 项
            </Text>
            <Group>
              <Button variant='light' onClick={close}>取消</Button>
              <Button onClick={handleConfirm}>确定</Button>
            </Group>
          </Group>
        </Stack>
      </Modal>
    </>
  )
} 
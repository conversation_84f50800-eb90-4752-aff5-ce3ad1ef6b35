import {
  Button,
  TextInput,
  Stack,
  Title,
  Box,
  Switch,
  ScrollArea,
  Group,
  Radio,
} from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import { UseFormReturnType } from '@mantine/form'
import { useState, useEffect } from 'react'

import PartnerSelect from '@/components/Form/PartnerSelector'
import CreatableCombobox from '@/components/Form/CreatableCombobox'

import PriceConfig from './PriceConfig'
import RulesConfig from './RulesConfig'
import { ContractWithPartner, SettleMethodEnum } from '../helper/types'
import formStyles from '../css/form.module.css'
import { usePartner } from '../hooks/usePartner'
import { contractToServerContract } from '../helper/contractHelper'
import { wrapFetch } from '@/APIs'
import { notifications } from '@mantine/notifications'

interface ContractFormProps {
  onClose: () => void
  form: UseFormReturnType<ContractWithPartner>
  isEditing: boolean
  submit?: () => void
}

export default function ContractForm({ form, isEditing, onClose, submit }: ContractFormProps) {

  const {
    vendors,
    contractPartners,
   
    addContractPartners,
  } = usePartner()

  const [producerReadonly, setProducerReadonly] = useState(false)
  const [partnerOptions, setPartnerOptions] = useState<string[]>([])

  useEffect(() => {
    const options = contractPartners
      .filter(partner => partner.partnerId === form.values.partnerId)
      .map(partner => partner.orderProducerId)

    setPartnerOptions(options)
  }, [contractPartners, form.values.partnerId])

  const handlePartnerSelect = (value: string | null) => {
    console.log('select', value)
    if (value) {
      form.setFieldValue('orderProducerId', value)
      const selectedProducer = contractPartners.find(p => p.orderProducerId === value)
      if (selectedProducer) {
        form.setFieldValue('orderProducerName', selectedProducer.name)
        setProducerReadonly(true)
      } else {
        form.setFieldValue('orderProducerName', '')
        setProducerReadonly(false)
      }
    }
  }

  const handleAddPartner = async (searchValue: string) => {
    console.log('new', searchValue)
    if (searchValue) {
      // await addContractPartners() 等输入名称保存表单后再发起这个请求。
      form.setFieldValue('orderProducerId', searchValue)
    }
  }

    // 保存合同
    const onSubmit = form.onSubmit(async (values) => {
      const { partnerId, ...serverContract } = values
      const {_id, ...requestData} = { ...contractToServerContract(serverContract) }
  
      const [apiName, queriesData, titleType] = isEditing && _id
      ? ['updateContract', { queries: { _id } }, '更新']
      : ['addContract', { }, '新增']
  
      // 判断orderProducerId是否在contractPartners中，如果没有，发起请求
      if (!partnerOptions.includes(values.orderProducerId)) {
        addContractPartners({
          orderProducerId: values.orderProducerId,
          name: values.orderProducerName,
          partnerId,
        })
      }
  
      const [err, res] = await wrapFetch(
        apiName as "updateContract" | "addContract",
        {
          requestData,
          ...queriesData
        }
      )
  
      if (!err && res?.code === 0) {
        notifications.show({
          title: `${titleType}合同成功`,
          message: '合同信息已保存',
          color: 'green',
        })
        close()
        submit?.()
      } else {
        notifications.show({
          title: `${titleType}合同失败`,
          message: res?.message || '请稍后重试',
          color: 'red',
        })
      }
    })

  return (
    <Stack>
      <form onSubmit={onSubmit}>
        <ScrollArea h='calc(100vh - 240px)'>
          <Title order={4} className="mb-2">基本信息</Title>

          {!isEditing &&
            <PartnerSelect
              className='mr-2 flex items-center'
              classNames={{ ...formStyles }}
              label="归属渠道"
              placeholder="请选择归属渠道"
              withAsterisk
              {...form.getInputProps('partnerId')}
            />
          }

          <CreatableCombobox
            className='flex items-center'
            classNames={{ ...formStyles }}
            label="甲方id"
            placeholder="请输入甲方id"
            withAsterisk
            value={form.getValues().orderProducerId}
            data={partnerOptions}
            setData={setPartnerOptions}
            onCreate={handleAddPartner}
            onChange={handlePartnerSelect}
          />

          <TextInput
            className='flex items-center'
            classNames={{ ...formStyles }}
            label="甲方名称"
            placeholder="请输入甲方名称"
            withAsterisk
            disabled={producerReadonly}
            {...form.getInputProps('orderProducerName')}
          />

          <TextInput
            className='flex items-center'
            classNames={{ ...formStyles }}
            label="合同名称"
            placeholder="请输入合同名称"
            {...form.getInputProps('name')}
            withAsterisk
          />

          <DatePickerInput
            className='flex items-center'
            classNames={{ ...formStyles }}
            label="合同有效期"
            placeholder="请选择合同有效期"
            type="range"
            withAsterisk
            clearable
            valueFormat='YYYY/MM/DD'
            firstDayOfWeek={0}
            {...form.getInputProps('validPeriod')}
          />

          <Radio.Group
            label="结算方式"
            withAsterisk
            className='mt-4 flex items-center'
            classNames={{ ...formStyles }}
            {...form.getInputProps('settleMethod')}
          >
            <Group>
              {Object.entries(SettleMethodEnum).map(([value, label]) => (
                <Radio key={value} value={label} label={label} />
              ))}
            </Group>
          </Radio.Group>

          <Radio.Group
            label="支付主体"
            className='flex items-center'
            classNames={{ ...formStyles }}
            {...form.getInputProps('paymentEntity')}
            withAsterisk
          >
            <Group>
              <Radio value="haola" label="好啦" />
              <Radio value="partner" label="合作方" />
            </Group>
          </Radio.Group>

          <Switch
            classNames={{ ...formStyles }}
            label="是否核保"
            labelPosition="left"
            {...form.getInputProps('isUnderwriting')}
          />

          <Box my='lg'>
            <Title order={4} className="mb-2">价格配置</Title>
            <PriceConfig form={form} vendors={vendors} />
          </Box>

          <Box my='lg'>
            <Title order={4} className="mb-2">甲方规则配置</Title>
            <RulesConfig form={form} />
          </Box>
        </ScrollArea>
        <Group justify='end' mt="xs">
          <Button variant="outline" className='w-24' radius="md" onClick={onClose}>取消</Button>
          <Button className='w-24' radius="md" type="submit">保存</Button>
        </Group>
      </form>
    </Stack>
  )
} 
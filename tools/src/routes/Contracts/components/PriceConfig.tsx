import { Box, Group, Radio, Select, NumberInput, Title, Button, Stack } from '@mantine/core'
import { UseFormReturnType } from '@mantine/form'
import { PlusIcon, TrashIcon } from '@radix-ui/react-icons'

import { BaseItemPriceConfig, Vendor, ContractWithPartner } from '../helper/types'
import ItemsSelector from './ItemsSelector'

import formStyles from '../css/form.module.css'

interface PriceConfigProps {
  form: UseFormReturnType<ContractWithPartner>
  vendors: Vendor[]
}

const PRICE_TYPES = [
  { value: 'coefficient', label: '系数定价' },
  { value: 'number', label: '固定价格' },
] as const

const PRICE_BASES = [
  { value: 'marketPrice', label: '市场价' },
  { value: 'displayPrice', label: '展示价' },
  { value: 'salePrice', label: '销售价' },
  { value: 'settlementPrice', label: '结算价' },
] as const

const defaultPriceConfig: BaseItemPriceConfig = {
  orderConsumerId: '',
  itemIds: [],
  type: 'coefficient',
  displayBase: 'marketPrice',
  displayCoefficient: 1,
  saleBase: 'marketPrice',
  saleCoefficient: 1,
  settlementBase: 'marketPrice',
  settlementCoefficient: 1,
}

export default function PriceConfig({ form, vendors }: PriceConfigProps) {
  const addPriceConfig = () => {
    form.setFieldValue('priceConfig', [...form.values.priceConfig, { ...defaultPriceConfig }])
  }

  const removePriceConfig = (index: number) => {
    form.setFieldValue(
      'priceConfig',
      form.values.priceConfig.filter((_, i) => i !== index)
    )
  }

  const renderCoefficientConfig = (index: number) => (
    <>
      <Group grow mt="md" justify='space-between'>
        <Select
          label="展示价基准"
          placeholder="请选择展示价基准"
          data={PRICE_BASES}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.displayBase`)}
        />
        <NumberInput
          label="展示价系数"
          placeholder="请输入展示价系数"
          min={0}
          step={0.01}
          decimalScale={2}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.displayCoefficient`)}
        />
      </Group>

      <Group grow mt="md" justify='space-between'>
        <Select
          label="销售价基准"
          placeholder="请选择销售价基准"
          data={PRICE_BASES}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.saleBase`)}
        />
        <NumberInput
          label="销售价系数"
          placeholder="请输入销售价系数"
          min={0}
          step={0.01}
          decimalScale={2}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.saleCoefficient`)}
        />
      </Group>

      <Group grow mt="md" justify='space-between'>
        <Select
          label="结算价基准"
          placeholder="请选择结算价基准"
          data={PRICE_BASES}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.settlementBase`)}
        />
        <NumberInput
          label="结算价系数"
          placeholder="请输入结算价系数"
          min={0}
          step={0.01}
          decimalScale={2}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.settlementCoefficient`)}
        />
      </Group>
    </>
  )

  const renderFixedPriceConfig = (index: number) => (
    <>
      <ItemsSelector
        label="好啦最小项"
        className='flex items-center'
        classNames={{ ...formStyles }}
        selectedItems={form.getValues()['priceConfig'][index].itemIds}
        onSelect={val => form.setFieldValue(`priceConfig.${index}.itemIds`, val)}
      ></ItemsSelector>
      <div className='grid grid-cols-2 gap-x-4 gap-y-1'>
        <NumberInput
          label="展示价"
          placeholder="请输入展示价"
          min={0}
          decimalScale={2}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.displayPrice`)}
        />
        <NumberInput
          label="销售价"
          placeholder="请输入销售价"
          min={0}
          decimalScale={2}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.salePrice`)}
        />
        <NumberInput
          label="结算价"
          placeholder="请输入结算价"
          min={0}
          decimalScale={2}
          className='flex items-center'
          classNames={{ ...formStyles, wrapper: formStyles.numberWrapper }}
          {...form.getInputProps(`priceConfig.${index}.settlementPrice`)}
        />
      </div>
    </>
  )

  const renderPriceConfig = (config: BaseItemPriceConfig, index: number) => {
    const map = {
      coefficient: renderCoefficientConfig,
      number: renderFixedPriceConfig,
    }
    return map[config.type](index)
  }

  return (
    <Box my='lg'>
      <Stack gap="lg">
        {form.values.priceConfig?.map((config, index) => (
          <Box key={index} p="md" style={{ border: '1px solid #eee', borderRadius: '4px' }}>
            <Group justify="space-between" mb="md">
              <Title order={4}>价格配置 {index + 1}</Title>
              <Button
                variant="subtle"
                color="red"
                leftSection={<TrashIcon />}
                onClick={() => removePriceConfig(index)}
              >
                删除
              </Button>
            </Group>

            <Group grow mt="md">
              <Select
                label="乙方"
                withAsterisk
                data={vendors.map(v => ({ value: v.orderConsumerId, label: v.name }))}
                className='flex items-center'
                classNames={{ ...formStyles }}
                {...form.getInputProps(`priceConfig.${index}.orderConsumerId`)}
              />
            </Group>

            <Radio.Group
              label="定价模式"
              withAsterisk
              className='mb-8 flex items-center'
              classNames={{ ...formStyles }}
              {...form.getInputProps(`priceConfig.${index}.type`)}
            >
              <Group>
                {PRICE_TYPES.map(type => (
                  <Radio key={type.value} value={type.value} label={type.label} />
                ))}
              </Group>
            </Radio.Group>

            {renderPriceConfig(config, index)}
          </Box>
        ))}

        <Button
          variant="light"
          leftSection={<PlusIcon />}
          onClick={addPriceConfig}
        >
          添加价格配置
        </Button>
      </Stack>
    </Box>
  )
} 
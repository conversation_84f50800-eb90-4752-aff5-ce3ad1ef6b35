import { useState } from 'react'
import { useDisclosure } from '@mantine/hooks'
import { useForm } from '@mantine/form'

import { wrapFetch } from '@/APIs'
import { ServerContract, ContractWithPartner, SettleMethodEnum } from '../helper/types'
import { serverContractToContract } from '../helper/contractHelper'


// 初始合同数据
const initialContract: ContractWithPartner = {
  partnerId: '',
  _id: '',
  contractId: '',
  updateDate: '',
  orderProducerId: '',
  orderProducerName: '',
  name: '',
  validPeriod: [
    new Date(),
    new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
  ],
  settleMethod: SettleMethodEnum.SelfPay,
  paymentEntity: 'haola',
  isUnderwriting: false,
  settlementPeriod: 'monthly',
  rules: [],
  priceConfig: [],
  contactMobile: '',
  contactName: '',
  internalContact: '',
  internalContactMobile: '',
}

export function useContract() {
  const [contracts, setContracts] = useState<ContractWithPartner[]>([])
  const [selectedPartner, setSelectedPartner] = useState<string | null>('')

  const [isEditing, setIsEditing] = useState(false)
  const [opened, { open, close }] = useDisclosure(false)

  const form = useForm({
    mode: 'controlled',
    initialValues: initialContract,
    validate: {
      // partnerId: (value) => (value ? true : '请选择合作商'),
      // orderProducerName: (value) => (value ? true : '请输入公司名称'),
      // name: (value) => (value ? true : '请输入合同名称'),
    }
  })

  // 获取合同列表
  const fetchContracts = async (partnerId?: string | null) => {
    if (!partnerId) return
    const [err, res] = await wrapFetch<{ data: { docs: ServerContract[] } }>('partnerContracts', { requestData: { partnerId } })
    if (err || res?.code !== 0) return
    setContracts((res.data.docs || []).map(contract => ({ ...serverContractToContract(contract), partnerId })))
  }

  // 打开新增合同模态框
  const handleAddContract = () => {
    form.setValues(initialContract)
    setIsEditing(false)
    open()
  }

  // 打开编辑合同模态框
  const handleEditContract = (contractData: ContractWithPartner) => {
    console.log('handle--edit', contractData)
    form.setValues(contractData)
    setIsEditing(true)
    open()
  }



  return {
    contracts,
    selectedPartner,
    isEditing,
    opened,
    close,
    form,
    fetchContracts,
    handleAddContract,
    handleEditContract,
    setSelectedPartner
  }
}



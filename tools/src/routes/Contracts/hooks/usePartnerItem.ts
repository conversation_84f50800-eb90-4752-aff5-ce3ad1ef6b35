import { useState } from 'react'
import { useForm } from '@mantine/form'
import { notifications } from '@mantine/notifications'
import { useDisclosure } from '@mantine/hooks'
import { useAtomValue, useSetAtom } from 'jotai'

import { wrapFetch } from '@/APIs'
import { partnerItemsAtom, partnerItemsLoadingAtom, fetchPartnerItemsAtom,type PartnerItem } from '@/store/checkItems'

  
type FormData = Omit<PartnerItem, 'beginDate' | 'endDate' | '_id'> & { validPeriod: [Date, Date] }

export function usePartnerItem(contractId: string) {
  const partnerItems = useAtomValue(partnerItemsAtom)
  const loading = useAtomValue(partnerItemsLoadingAtom)
  const refreshPartnerItems = useSetAtom(fetchPartnerItemsAtom)

  const [opened, { open, close }] = useDisclosure(false) 
  const [editingId, setEditingId] = useState<string|null>()

  const form = useForm<{ partnerItem: FormData }>({
    mode: 'controlled',
    initialValues: { 
      partnerItem: {
        itemId: '',
        orderConsumerId: 'allPE',
        baseItems: [],
        validPeriod: [
          new Date(),
          new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        ],
        displayPrice: '',
        name: '',
        sellingPrice: '',
        settlementPrice: '',
        orderProducerContractId: contractId
      }
    },
  })

  const addPartnerItem = () => {
    setEditingId('')
    open()
  }

  const handleEdit = (index: number) => {
    const item = partnerItems[index]
    setEditingId(item._id)
    open()

    form.setFieldValue('partnerItem', {
      ...item,
      validPeriod: [new Date(item.beginDate), new Date(item.endDate)] as [Date, Date]
    })
  }
  const addItem = async (item: Omit<PartnerItem, '_id'>) => {
    const [err, res] = await wrapFetch<{ _id: string }>('addPartnerItem', {
      requestData: {
        ...item
      }
    })
    if (!err && res?.code !== 0) {
      return true
    }
    return false
  }

  const updateItem = async (item: PartnerItem) => {
    if (!item._id) return false
    const [err] = await wrapFetch('updatePartnerItem', {
      requestData: {
        ...item
      },
      queries: { id: item._id }
    })
    if (!err) {
      return true
    }
    return false
  }

  const onsubmit = form.onSubmit(async values => {
    const formData = values.partnerItem

    const partnerItem = {
      ...formData,
      beginDate: formData.validPeriod[0].toISOString(),
      endDate: formData.validPeriod[1].toISOString(),
    }
    let success = false

    if (editingId) {
      success = await updateItem({
        ...partnerItem,
        _id: editingId
      })
    } else {
      success = await addItem(partnerItem)
    }
    close()

    if (success) {
      notifications.show({
        message: '保存成功'
      })
      refreshPartnerItems({contractId, update: true})
    }
  })

  return {
    form,
    loading,
    partnerItems,
    editingId,
    opened,
    close,

    addPartnerItem,
    handleEdit,

    onsubmit
  }
}

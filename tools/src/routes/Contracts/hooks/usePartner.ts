import { wrapFetch } from '@/APIs'
import { useEffect, useState } from 'react'
import { Vendor } from '../helper/types'

export interface ContractPartner {
  id: string
  name: string
  orderProducerId: string
  partnerId: string
  parent?: string
}

export function usePartner() {
  const [contractPartners, setContractPartners] = useState<ContractPartner[]>(
    []
  )
  const [vendors, setVendors] = useState<Vendor[]>([])

  // 获取已有的合同甲方列表
  const fetchContractPartners = async () => {
    const [err, res] = await wrapFetch<{ data: { docs: ContractPartner[] } }>(
      'contractPartners'
    )
    if (!err && res?.code === 0) {
      setContractPartners(res?.data?.docs || [])
    }
  }
  // 新增甲方
  const addContractPartners = async (requestData: Record<string, string>) => {
    const [err, res] = await wrapFetch(
      'addContractPartner',
      { requestData }
    )
    if (!err && res?.code === 0) {
      fetchContractPartners()
    }
  }

  // 获取所有乙方
  const fetchVendors = async () => {
    const [err, res] = await wrapFetch<{ data: { docs: Vendor[] } }>(
      'allVendors'
    )
    if (!err && res?.code === 0) {
      setVendors(res?.data?.docs.filter(v => v.parents.includes('allPE')) || [])
    }
  }
  useEffect(() => {
    fetchContractPartners()
    fetchVendors()
  }, [])

  return {
    vendors,
    contractPartners,
    addContractPartners
  }
}

import {
  Button,
  Modal,
  Group,
  Stack,
  Paper,
  Title,
} from '@mantine/core'
import { PlusIcon, MagnifyingGlassIcon, Pencil2Icon, GearIcon } from '@radix-ui/react-icons'
import { useState, useEffect } from 'react'
import { useSetAtom } from 'jotai'

import { fetchHlItemsAtom, fetchPartnerItemsAtom } from '@/store/checkItems'
import PartnerSelect from '@/components/Form/PartnerSelector'
import PartnerItem from './components/PartnerItem'
import ContractForm from './components/ContractForm'
import ContractTable from './components/ContractTable'

import { useContract } from './hooks/useContract'
import { ContractWithPartner } from './helper/types'

import modalStyles from './css/index.module.css'
import formStyles from './css/form.module.css'

export default function Contracts() {
  const fetchHlItems = useSetAtom(fetchHlItemsAtom)
  const fetchPartnerItems = useSetAtom(fetchPartnerItemsAtom)

  useEffect(() => {
    fetchHlItems()
  }, [fetchHlItems])

  const {
    form,
    contracts,
    selectedPartner,
    isEditing,
    opened,
    close,
    fetchContracts,
    handleAddContract,
    handleEditContract,
    setSelectedPartner,
  } = useContract()

  const [partnerItemModalOpened, setPartnerItemModalOpened] = useState(false)
  const [selectedContractId, setSelectedContractId] = useState<string>('')

  const handlePartnerItemsModal = (contract: ContractWithPartner) => {
    setSelectedContractId(contract.contractId)
    fetchPartnerItems({contractId: contract.contractId})
    setPartnerItemModalOpened(true)
  }

  const submitCallback = () => {
    fetchContracts(selectedPartner)
  }

  return (
    <div className="p-4">
      <Stack gap="md">
        <Paper shadow="xs" p="md" className="mb-6">
          <Title order={3}>合同管理</Title>

          <div className="flex items-center my-4">
            <PartnerSelect
              className='mr-2'
              value={selectedPartner}
              onChange={setSelectedPartner}
            />
            <Button
              leftSection={<MagnifyingGlassIcon />}
              onClick={() => fetchContracts(selectedPartner)}
              className="mr-2"
            >
              查询
            </Button>
            <Button
              leftSection={<PlusIcon />}
              onClick={handleAddContract}
              variant='light'
            >
              新增合同
            </Button>
          </div>
        </Paper>

        <Paper shadow="xs" p="md">
          <ContractTable 
            contracts={contracts}
            actions={[
              {
                accessor: 'actions',
                title: '操作',
                width: 160,
                render: (record: Record<string, unknown>) => {
                  const contract = record as ContractWithPartner
                  return (
                    <Group gap='xs'>
                      <Button
                        variant='subtle'
                        size="xs"
                        classNames={{ root: formStyles.ButtonRoot, section: formStyles.ButtonSection }}
                        leftSection={<Pencil2Icon />}
                        onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                          e.stopPropagation()
                          handleEditContract(contract)
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        variant="subtle"
                        size="xs"
                        classNames={{ root: formStyles.ButtonRoot, section: formStyles.ButtonSection }}
                        leftSection={<GearIcon />}
                        onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                          e.stopPropagation()
                          handlePartnerItemsModal(contract)
                        }}
                      >
                        项目配置
                      </Button>
                    </Group>
                  )
                },
              },
            ]}
          />
        </Paper>
      </Stack>

      {/* 新增/编辑合同模态框 */}
      <Modal
        opened={opened}
        onClose={close}
        title={isEditing ? "编辑合同" : "新增合同"}
        size="80%"
        yOffset='8vh'
        transitionProps={{ transition: 'fade', duration: 100, timingFunction: 'linear' }}
        classNames={{ content: modalStyles.modalContent, title: modalStyles.modalTitle }}
      >
        <ContractForm
          form={form}
          isEditing={isEditing}
          onClose={close}
          submit={submitCallback}
        />
      </Modal>

      <Modal
        opened={partnerItemModalOpened}
        onClose={() => setPartnerItemModalOpened(false)}
        title="甲方项目配置"
        size="80%"
        classNames={{ content: modalStyles.modalContent, title: modalStyles.modalTitle }}
      >
        <PartnerItem contractId={selectedContractId} close={() => setPartnerItemModalOpened(false)}/>
      </Modal>
    </div>
  )
}
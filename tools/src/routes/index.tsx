import {
  createHashRouter,
  Navigate,
  RouterProvider,
} from 'react-router-dom'
import { lazy, useState } from 'react'
import { Affix, AppShell, Burger, Group, Button, Transition, NavLink } from '@mantine/core'
import { ArrowUpIcon } from '@radix-ui/react-icons'
import { useDisclosure, useWindowScroll } from '@mantine/hooks'

import { useAuth } from '@/context/useAuth'

import LoginForm from '@/components/Auth/LoginForm'
import PeEntries from './PeEntries'
import DragAndDrop from './DragAndDrop'
import WelfareMapping from './WelfareMapping'
import JSONComparator from './JSONComparator'
import DBFetcher from './DBFetcher'
import ContractManagement from './Contracts'

const Account = lazy(() => import('./Account'))

function ProtectedRoute({ element: Element }: { element: React.ComponentType }) {
  const { isLoggedIn } = useAuth()
  console.log('isLoggedIn', isLoggedIn)
  if (!isLoggedIn) {
    return <LoginForm />
  }

  return (
    <Element />
  )
}
// 类型定义
type RouteConfig = {
  path: string;
  label?: string;
  element: React.ReactNode;
  protected?: boolean;
  showOnMenu?: boolean;
  icon?: React.ComponentType;
};

// 路由配置数组
const ROUTE_CONFIGS: RouteConfig[] = [
  {
    path: '/',
    label: '首页',
    element: <Navigate to="/pe-link" replace />,
  },
  {
    path: '/pe-link',
    label: '渠道链接',
    element: <PeEntries />,
    showOnMenu: true,
  },
  {
    path: '/welfare-mapping',
    label: '团单切换最小项',
    element: <WelfareMapping />,
  },
  {
    path: '/account',
    element: <ProtectedRoute element={Account} />,
    label: '企健结算对账',
    showOnMenu: true,
  },
  {
    path: '/JSON-comparator',
    element: <JSONComparator />,
    label: 'JSON脚本工作台',
    showOnMenu: true,
  },
  {
    path: '/dnd',
    element: <DragAndDrop />,
  },
  {
    path: '/db-fetcher',
    element: <DBFetcher />,
  },
  {
    path: '/contracts',
    label: '甲方合同',
    element: <ProtectedRoute element={ContractManagement} />,
    showOnMenu: true
  }
];

// 生成路由
const router = createHashRouter(
  ROUTE_CONFIGS.map(config => ({
    path: config.path,
    element: config.element
  }))
)

export function Routes() {
  const [scroll, scrollTo] = useWindowScroll()
  const [mobileOpened, { toggle: toggleMobile }] = useDisclosure()
  const [desktopOpened, { toggle: toggleDesktop }] = useDisclosure(false)
  const [active, setActive] = useState(0)

  return (
    <>
      <AppShell
        // header={{ height: 40, collapsed: !pinned }}
        navbar={{
          width: 200,
          breakpoint: 'sm',
          collapsed: { mobile: !mobileOpened, desktop: !desktopOpened },
        }}
        padding="xs"
        zIndex={10}
      >
        <AppShell.Navbar p="sm" withBorder={false} className='shadow-inner shadow-sky-100'>
          <div className='pt-10'>
            {
              ROUTE_CONFIGS.filter(route => route.showOnMenu).map((route, index) => (
                <NavLink
                  key={index}
                  href={route.path?.replace('/', '#') || ''}
                  label={route.label ?? ''}
                  active={active === index}
                  onClick={() => setActive(index)}
                />
              ))
            }
          </div>
        </AppShell.Navbar>
        <AppShell.Main>
          <Group className='fixed z-20 top-2 left-3'>
            <Burger opened={mobileOpened} onClick={toggleMobile} hiddenFrom="sm" size="sm" />
            <div className='w-8 h-8 flex items-center justify-center bg-cyan-50 rounded-full shadow-md shadow-slate-300'>
              <Burger opened={desktopOpened} onClick={toggleDesktop} color='cyan' visibleFrom="xs" size="xs" />
            </div>
          </Group>

          <RouterProvider router={router} />
          <Affix position={{ bottom: 20, right: 20 }}>
            <Transition transition="slide-up" mounted={scroll.y > 0}>
              {(transitionStyles) => (
                <Button
                  leftSection={<ArrowUpIcon />}
                  style={transitionStyles}
                  onClick={() => scrollTo({ y: 0 })}
                >
                  返回顶部
                </Button>
              )}
            </Transition>
          </Affix>
        </AppShell.Main>
      </AppShell>

    </>
  )
}
import { CSSProperties } from 'react'

interface ExpandIconProps {
  expanded: boolean
  size?: number
  style?: CSSProperties
  className?: string
}

const ExpandIcon = ({ expanded, size = 16, style, className }: ExpandIconProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      style={style}
      className={className}
    >
      <circle
        cx="12"
        cy="12"
        r="10"
        className="fill-gray-100 stroke-gray-300"
      />
      <g
        className="fill-none stroke-gray-400 stroke-2"
        style={{
          transformOrigin: 'center',
          transition: 'transform 0.2s ease'
        }}
      >
        {/* 横线 */}
        <line x1="8" y1="12" x2="16" y2="12" />
        {/* 竖线，根据expanded状态旋转 */}
        <line
          x1="12"
          y1="8"
          x2="12"
          y2="16"
          style={{
            transformOrigin: 'center',
            transform: `scaleY(${expanded ? 0 : 1})`,
            transition: 'transform 0.2s ease'
          }}
        />
      </g>
    </svg>
  )
}

export default ExpandIcon
interface Props {
  width?: number
  height?: number
  className?: string
  fill?: string
}
function Upload(props: Props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" widths="24" height="24" fill="#1296db" {...props} viewBox="0 0 1024 1024" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M847.82408481 949.99165771h-658.49653874c-85.73258697 0-155.47835273-69.74576542-155.47835273-155.4783525v-219.49884653a27.43735011 27.43735011 0 1 1 54.87471546 0v219.49884653c0 55.46004655 45.12530401 100.60363718 100.60363727 100.60363722h658.49653874c55.46004655 0 100.60363718-45.14359084 100.60363754-100.60363722v-219.49884653a27.43735011 27.43735011 0 0 1 54.87471523 0v219.49884653c0 85.73258697-69.76405255 155.47835273-155.47835277 155.4783525zM427.11796413 254.91197553a27.43735011 27.43735011 0 0 1-19.40735815-46.82642172l110.86521703-110.86520197 110.84691502 110.86520197a27.43735011 27.43735011 0 1 1-38.79642922 38.79642954L518.57582301 174.83148236l-72.05050061 72.05050099a27.34590075 27.34590075 0 0 1-19.40735827 8.02999218z" fill="#1296db" p-id="6190"></path><path d="M518.57582301 776.22173347a27.43735011 27.43735011 0 0 1-27.43736538-27.43735018v-612.76761693a27.43735011 27.43735011 0 1 1 54.87471547 0v612.76761693a27.43735011 27.43735011 0 0 1-27.43735009 27.43735018z"></path>
    </svg>
  )
}

export default Upload
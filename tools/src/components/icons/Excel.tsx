interface Props {
  width?: number
  height?: number
  className?: string
}
function Excel(props: Props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" widths="24" height="24" {...props} viewBox="0 0 1024 1024" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M931.84 655.36c12.288 0 20.48-8.192 20.48-20.48V296.96l-163.84-167.936h-456.704c-45.056 0-81.92 36.864-81.92 81.92v67.584h-137.216c-22.528 0-40.96 18.432-40.96 40.96v405.504c0 22.528 18.432 40.96 40.96 40.96h137.216v53.248c0 45.056 36.864 81.92 81.92 81.92h538.624c45.056 0 81.92-36.864 81.92-81.92v-61.44c0-12.288-8.192-20.48-20.48-20.48s-20.48 8.192-20.48 20.48v61.44c0 22.528-18.432 40.96-40.96 40.96h-538.624c-22.528 0-40.96-18.432-40.96-40.96v-53.248h225.28c22.528 0 40.96-18.432 40.96-40.96v-405.504c0-22.528-18.432-40.96-40.96-40.96h-225.28v-67.584c0-22.528 18.432-40.96 40.96-40.96h415.744v106.496c0 32.768 26.624 61.44 61.44 61.44h102.4v296.96c0 12.288 8.192 20.48 20.48 20.48z m-618.496-165.888l45.056-65.536h61.44l-71.68 102.4 86.016 118.784h-63.488l-55.296-77.824-55.296 77.824h-63.488l86.016-118.784-73.728-102.4h63.488l40.96 65.536z m495.616-192.512c-12.288 0-20.48-8.192-20.48-20.48v-90.112l106.496 110.592h-86.016z" fill="#32373B"></path>
      <path d="M768 614.4h-102.4c-12.288 0-20.48 8.192-20.48 20.48s8.192 20.48 20.48 20.48h102.4c12.288 0 20.48-8.192 20.48-20.48s-8.192-20.48-20.48-20.48zM768 512h-102.4c-12.288 0-20.48 8.192-20.48 20.48s8.192 20.48 20.48 20.48h102.4c12.288 0 20.48-8.192 20.48-20.48s-8.192-20.48-20.48-20.48zM768 409.6h-102.4c-12.288 0-20.48 8.192-20.48 20.48s8.192 20.48 20.48 20.48h102.4c12.288 0 20.48-8.192 20.48-20.48s-8.192-20.48-20.48-20.48z" fill="#32373B"></path>
    </svg>
  )
}

export default Excel
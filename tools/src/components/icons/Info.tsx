interface Props {
  width?: number
  height?: number
  className?: string
  fill?: string
}
export default function Info(props: Props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" widths="24" height="24" fill="#3386F1" {...props} viewBox="0 0 1024 1024" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M512 0c282.331429 0 512 229.668571 512 512s-229.668571 512-512 512-512-229.668571-512-512 229.668571-512 512-512z m0 394.313143a47.981714 47.981714 0 0 0-47.981714 47.981714v309.686857a47.981714 47.981714 0 0 0 95.963428 0V442.294857A47.981714 47.981714 0 0 0 512 394.313143z m0-175.981714a64 64 0 1 0 0 128 64 64 0 0 0 0-128z" fill="#3386F1" ></path>
    </svg>
  )
}
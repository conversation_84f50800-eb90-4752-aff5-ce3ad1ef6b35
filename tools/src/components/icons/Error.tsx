interface Props {
  width?: number
  height?: number
  className?: string
  fill?: string
}
function Error(props: Props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" widths="24" height="24" fill="#d81e06" {...props} viewBox="0 0 1024 1024" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M512 451.669333L115.498667 55.168a42.666667 42.666667 0 1 0-60.330667 60.330667L451.669333 512 55.168 908.501333a42.666667 42.666667 0 0 0 60.330667 60.330667L512 572.330667l396.501333 396.501333a42.666667 42.666667 0 0 0 60.330667-60.330667L572.330667 512 968.832 115.498667a42.666667 42.666667 0 0 0-60.330667-60.330667L512 451.669333z"></path>
    </svg>
  )
}

export default Error


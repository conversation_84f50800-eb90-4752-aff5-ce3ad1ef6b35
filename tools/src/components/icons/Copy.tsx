interface Props {
  width?: number
  height?: number
  className?: string
}
function Copy(props: Props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" widths="24" height="24" {...props} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M0 0h24v24H0z" stroke="none" />
      <path d="M7 9.667A2.667 2.667 0 0 1 9.667 7h8.666A2.667 2.667 0 0 1 21 9.667v8.666A2.667 2.667 0 0 1 18.333 21H9.667A2.667 2.667 0 0 1 7 18.333z" />
      <path d="M4.012 16.737A2 2 0 0 1 3 15V5c0-1.1.9-2 2-2h10c.75 0 1.158.385 1.5 1" />
    </svg>
  )
}

export default Copy
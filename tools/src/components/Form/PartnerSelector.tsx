import { Select, SelectProps } from '@mantine/core'
import { useAtom } from "jotai"

import { partnerGroupsAtom } from "@/store/partners"

interface PartnerSelectProps extends Omit<SelectProps, 'data'> {
  // 可以添加额外的属性
  autoFetch?: boolean;
  className?: string;
}

const PartnerSelect = ({
  placeholder = "请选择渠道",
  searchable = true,
  clearable = true,
  ...props
}: PartnerSelectProps) => {
  const [partnerGroups] = useAtom(partnerGroupsAtom)

  return (
    <Select
      data={partnerGroups}
      placeholder={placeholder}
      searchable={searchable}
      clearable={clearable}
      {...props}
    />
  )
}

export default PartnerSelect
import { Combobox, InputBase, useCombobox, TextInputProps } from '@mantine/core'
import { useState } from 'react'

type SelectCreatableProps = {
  data: string[]
  setData: React.Dispatch<React.SetStateAction<string[]>>
  onCreate?: (value: string) => void
  onChange?: (value: string | null) => void
} & Omit<TextInputProps, 'onChange'>

export default function SelectCreatable({
  data,
  setData,
  onCreate,
  onChange,
  ...props
}: SelectCreatableProps) {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  })

  const [value, setValue] = useState<string | null>(null)
  const [search, setSearch] = useState('')

  const exactOptionMatch = data.some((item) => item === search)
  const filteredOptions = exactOptionMatch
    ? data
    : data.filter((item) => item.toLowerCase().includes(search.toLowerCase().trim()))

  const options = filteredOptions.map((item) => (
    <Combobox.Option value={item} key={item}>
      {item}
    </Combobox.Option>
  ))

  return (
    <Combobox
      store={combobox}
      withinPortal={false}
      onOptionSubmit={(val) => {
        if (val === '$create') {
          setData((current) => [...current, search])
          setValue(search)
          onChange?.(search)
          onCreate?.(search)
        } else {
          setValue(val)
          setSearch(val)
          onChange?.(val)
        }
        combobox.closeDropdown()
      }}
    >
      <Combobox.Target>
        <InputBase
          value={search}
          onChange={(event) => {
            combobox.openDropdown()
            combobox.updateSelectedOptionIndex()
            setSearch(event.currentTarget.value)
          }}
          onClick={() => combobox.openDropdown()}
          onFocus={() => combobox.openDropdown()}
          onBlur={() => {
            combobox.closeDropdown()
            setSearch(value || '')
          }}
          rightSectionPointerEvents="none"
          {...props}
        />
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Options>
          {options}
          {!exactOptionMatch && search.trim().length > 0 && (
            <Combobox.Option value="$create">+ 新建 {search}</Combobox.Option>
          )}
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  )
}
import { Group, TextInput, TextInputProps } from '@mantine/core'
import { PlusIcon } from '@radix-ui/react-icons'

type InputModalProps = {
  onFocus?: () => void
  className?: string
  classNames?: Record<string, string>
} & TextInputProps

export default function InputModal({
  onFocus,
  ...props
}: InputModalProps) {

  return (
    <Group align="flex-end" gap="xs">
      <TextInput
        readOnly
        rightSection={<PlusIcon
          onClick={onFocus}
        />}
        onClick={onFocus}
        {...props}
      />
    </Group>
  )
}


interface ZoneProps extends React.HTMLAttributes<HTMLDivElement> {
  selectionStart: { x: number; y: number }
  selectionEnd: { x: number; y: number }
}

const Zone = ({ selectionStart, selectionEnd, ...props }: ZoneProps) => {
  return (
    <div
      style={{
        position: 'absolute',
        left: Math.min(selectionStart.x, selectionEnd.x),
        top: Math.min(selectionStart.y, selectionEnd.y),
        width: Math.abs(selectionEnd.x - selectionStart.x),
        height: Math.abs(selectionEnd.y - selectionStart.y),
        backgroundColor: 'rgba(0, 123, 255, 0.1)',
        border: '1px solid rgba(0, 123, 255, 0.5)',
        pointerEvents: 'none',
      }}
      {...props}
    />
  )
}

export default Zone
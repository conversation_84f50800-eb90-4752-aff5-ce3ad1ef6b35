// 创建自定义拖拽图像
export function createDragImage(e: React.DragEvent<HTMLDivElement>, itemsToMove: string[]) {
  // 创建拖拽预览容器
  const dragImage = document.createElement('div')
  Object.assign(dragImage.style, {
    position: 'fixed',
    left: '-9999px',
    display: 'flex',
    flexDirection: 'column',
    gap: '4px',
    padding: '4px',
    pointerEvents: 'none',
    width: 'max-content',
    zIndex: '9999',
  })

  // 获取原始元素的样式
  const originalElement = e.currentTarget
  const computedStyle = window.getComputedStyle(originalElement)

  // 定义需要复制的样式属性
  const styleProps = ['padding', 'backgroundColor', 'border', 'borderRadius', 'margin', 'fontSize', 'color']
  const baseStyles = Object.fromEntries(
    styleProps.map(prop => [prop, computedStyle[prop as keyof CSSStyleDeclaration]])
  )

  // 创建拖拽预览元素
  const fragment = document.createDocumentFragment()
  itemsToMove.forEach((item, index) => {
    const itemEl = document.createElement('div')
    Object.assign(itemEl.style, {
      ...baseStyles,
      width: `${originalElement.offsetWidth}px`,
      transform: `translate(${index * 4}px, ${index * 4}px)`,
    })
    itemEl.textContent = item
    fragment.appendChild(itemEl)
  })
  dragImage.appendChild(fragment)
  document.body.appendChild(dragImage)

  // 设置自定义拖拽图像
  e.dataTransfer.setDragImage(dragImage, 20, 20)

  // 在下一帧删除临时元素
  requestAnimationFrame(() => {
    document.body.removeChild(dragImage)
  })
}
import React from 'react'

interface DraggableItemProps {
  value: string
  selected: boolean
  fromTarget: boolean
  onDragStart: (e: React.DragEvent<HTMLDivElement>, value: string, fromTarget: boolean) => void
}

const DraggableItem: React.FC<DraggableItemProps> = ({ value, selected, fromTarget, onDragStart }) => {
  return (
    <div
      className="draggable-item cursor-grab my-2 p-2.5 border border-gray-300"
      data-value={value}
      draggable
      onDragStart={(e) => onDragStart(e, value, fromTarget)}
      style={{
        backgroundColor: selected ? '#e3f2fd' : '#fff',
      }}
    >
      {value}
    </div>
  )
}

export default DraggableItem
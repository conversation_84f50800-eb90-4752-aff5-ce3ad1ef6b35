import React, { useState } from 'react'
import Zone from './Zone'
import { createDragImage } from './util'
import DraggableItem from './DraggableItem'

function DragToTargetArea() {
  const [dragZone, setDragZone] = useState('')
  const [options, setOptions] = useState(['Option 1', 'Option 2', 'Option 3', 'Option 4', 'Option 5', 'Option 6', 'Option 7', 'Option 8'])
  const [targetItems, setTargetItems] = useState<string[]>([])
  const [action, setAction] = useState('')
  const [selectionStart, setSelectionStart] = useState({ x: 0, y: 0 })
  const [selectionEnd, setSelectionEnd] = useState({ x: 0, y: 0 })
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>, type: string) => {
    // 检查点击是否在选项上
    const isClickOnItem = (e.target as HTMLElement).classList.contains('draggable-item')
    if (!isClickOnItem) {
      setAction('')
      setSelectedItems([])
    }
    setDragZone(type)

    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top;
    ['', 'drop', 'dragEnd'].includes(action) && setAction('mouseDown')
    setSelectionStart({ x, y })
    setSelectionEnd({ x, y })
  }

  // 处理鼠标松开事件
  const handleMouseUp = () => {
    selectedItems.length && setAction('mouseUp')
  }

  // 处理鼠标移动事件，更新选择框
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!['mouseDown'].includes(action)) return
    console.log('move action', action)
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    setSelectionEnd({ x, y })

    // 获取所有选项元素
    const items = e.currentTarget.getElementsByClassName('draggable-item')
    const selected: string[] = []

    // 计算选择框的边界
    const left = Math.min(selectionStart.x, x)
    const right = Math.max(selectionStart.x, x)
    const top = Math.min(selectionStart.y, y)
    const bottom = Math.max(selectionStart.y, y)

    // 检查每个选项是否在选择框内
    Array.from(items).forEach((item) => {
      const itemRect = item.getBoundingClientRect()
      const itemX = itemRect.left - rect.left + itemRect.width / 2
      const itemY = itemRect.top - rect.top + itemRect.height / 2

      if (itemX >= left && itemX <= right && itemY >= top && itemY <= bottom) {
        selected.push(item.getAttribute('data-value') || '')
      }
    })

    setSelectedItems(selected)
  }

  const handleDragEnd = () => {
    setAction('dragEnd')
  }

  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, option: string, fromTarget: boolean) => {
    const itemsToMove = selectedItems.length > 0 ? selectedItems : [option]
    setAction('dragStart')
    e.dataTransfer.setData('text/plain', JSON.stringify(itemsToMove))
    e.dataTransfer.setData('fromTarget', String(fromTarget))

    createDragImage(e, itemsToMove)
  }
  const handleDrop = (e: React.DragEvent<HTMLDivElement>, toTarget: boolean) => {
    e.preventDefault()
    setAction('drop')
    const items = JSON.parse(e.dataTransfer.getData('text/plain')) as string[]
    const fromTarget = e.dataTransfer.getData('fromTarget') === 'true'

    if (fromTarget !== toTarget) {
      if (toTarget) {
        setTargetItems([...targetItems, ...items])
        setOptions(options.filter(item => !items.includes(item)))
      } else {
        setOptions([...options, ...items])
        setTargetItems(targetItems.filter(item => !items.includes(item)))
      }
    }
    setSelectedItems([])
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }

  return (
    <div className='flex p-5 gap-5'>
      <div
        onMouseDown={(e) => handleMouseDown(e, 'options')}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        onDrop={(e) => handleDrop(e, false)}
        className='relative p-5 min-w-52 min-h-72 bg-slate-100'
      >
        <h3>选项列表</h3>
        {options.map((option) => (
          <DraggableItem
            key={option}
            value={option}
            selected={selectedItems.includes(option)}
            fromTarget={false}
            onDragStart={(e) => handleDragStart(e, option, false)}
          />
        ))}
        {action === 'mouseDown' && dragZone === 'options' && <Zone selectionStart={selectionStart} selectionEnd={selectionEnd} />}
      </div>

      {/* 右侧目标区域的代码结构类似，需要添加相同的框选功能 */}
      <div
        onMouseDown={(e) => handleMouseDown(e, 'target')}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
        onDrop={(e) => handleDrop(e, true)}
        className='relative p-5 min-w-52 min-h-72 bg-blue-100/60 border border-blue-100/50 border-dashed'
      >
        <h3>选区</h3>
        {targetItems.map((item) => (
          <DraggableItem
            key={item}
            value={item}
            selected={selectedItems.includes(item)}
            fromTarget={false}
            onDragStart={(e) => handleDragStart(e, item, true)}
          />
        ))}
        {action === 'mouseDown' && dragZone === 'target' && <Zone selectionStart={selectionStart} selectionEnd={selectionEnd} />}
      </div>
    </div>
  )
}
export default DragToTargetArea
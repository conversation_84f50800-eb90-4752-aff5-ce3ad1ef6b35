import { JsonInput, SegmentedControl, Table } from '@mantine/core'
import { useEffect, useState } from 'react'

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { transferStrToJson } from './toTable.js'

const labelOptionMap = {
  haola: '好啦id',
  partyA: '甲方id',
  partyB: '体检供应商id'
}
const labelOptions = Object.entries(labelOptionMap).map(([value, label]) => ({value, label }))
function ToExcel() {
  const [jsonLabel, setValue] = useState('haola')
  const [json1, setJson1] = useState('')
  const [json2, setJson2] = useState('')
  const [discrepancy, setDiscrepancy] = useState<{id: string, name: string}[]>([])

  useEffect(() => {
    if(json1 && json2) {
      const result = transferStrToJson(json1, json2)
      console.log('ddd', result)
      setDiscrepancy(result)
    }
  }, [json1, json2])

  return (
    <>
      <SegmentedControl
        value={jsonLabel}
        onChange={setValue}
        data={labelOptions}
      />
      <div className='flex w-full justify-between mt-2'>
        <JsonInput
          className='flex-1'
          label={labelOptionMap[jsonLabel as keyof typeof labelOptionMap]}
          placeholder="Textarea will autosize to fit the content"
          validationError="Invalid JSON"
          formatOnBlur
          autosize
          minRows={4}
          maxRows={10}
          value={json1}
          onChange={setJson1}
        />
        <JsonInput
          className='flex-1 ml-4'
          label="体检供应商id"
          placeholder="Textarea will autosize to fit the content"
          validationError="Invalid JSON"
          formatOnBlur
          autosize
          minRows={4}
          maxRows={10}
          value={json2}
          onChange={setJson2}
        />
      </div>
      <Table>
      <Table.Thead>
        <Table.Tr>
          <Table.Th>好啦最小项id</Table.Th>
          <Table.Th>好啦最小项名称</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {
          discrepancy.map(item => (
            <Table.Tr key={item.id}>
              <Table.Td>{item.id}</Table.Td>
              <Table.Td>{item.name}</Table.Td>
            </Table.Tr>
          ))
        }
      </Table.Tbody>
    </Table>
    </>
  )
}

export default ToExcel



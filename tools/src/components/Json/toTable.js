export function transferStrTo<PERSON>son(str, str2) {
  const items1 = JSON.parse(str)
  const items2 = JSON.parse(str2)
  const item2Obj = items2.reduce((obj, item) => {
    const hlItems = getHlItems(item)
    hlItems.forEach(hlItem => {
      obj[hlItem.hlCode] = item
    })
    return obj
  }, {})

  return items1.filter(item => {
    const hlItem = getHlItemFromProduct(item)
    if(!Array.isArray(hlItem)) {
      return !item2Obj[hlItem.id]
    }
    return hlItem.some(v => !item2Obj[v.id])
  })
}

function getHlItems(item) {
  if(item.hlItems) return item.hlItems
  if(item.items) {
    return item.items.map(v => getHlItems(v))
  }
  return []
}

function getHlItemFromProduct(item) {
  if(item.type === 'hlItem') return item
  if(item.hlItems) return item.hlItems
  if(item.items) {
    return item.items.map(v => getHlItemFromProduct(v))
  }
  return []
}
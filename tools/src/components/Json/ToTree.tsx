import { Tree, Group, RenderTreeNodePayload, useTree } from '@mantine/core'
import ExpandIcon from '@/components/icons/Expand'
import { useEffect, useMemo } from 'react'
import { genJsonTree } from '@/utils/share'

const JSONTree = ({ newJSON, field, onSelect }: { newJSON: string, field?: string, onSelect?: (arg: string[]) => void }) => {
  const tree = useTree()
  const treeData = useMemo(() => {
    try {
      const parsedJSON = JSON.parse(newJSON)
      return [genJsonTree(parsedJSON, field)]
    } catch {
      return []
    }
  }, [newJSON, field])

  useEffect(() => {
    if(!tree.selectedState?.length) return
    onSelect?.(tree.selectedState)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tree.selectedState])

  return (
    <Tree
      tree={tree}
      data={treeData}
      levelOffset={23}
      selectOnClick
      clearSelectionOnOutsideClick
      renderNode={(payload) => <Leaf {...payload} />}
    />
  )
}

function Leaf({ node, expanded, hasChildren, elementProps }: RenderTreeNodePayload) {
  return (
    <Group gap={5} {...elementProps}>
      {hasChildren ? (
            <ExpandIcon expanded={expanded} size={16} />
          ) : (
            <div className="ml-2 mt-1 w-2 h-[1px] border-t border-dashed border-gray-300" />
          )}
          {typeof node?.label === 'string' ? node.label.split('##').map((str, index) => {
            return <span key={str} className={index === 0 ? 'text-blue-600' : 'before:content-[":"] before:mr-1 text-slate-600'} >{str}</span>
          }) : node.label}
    </Group>
  );
}

export default JSONTree
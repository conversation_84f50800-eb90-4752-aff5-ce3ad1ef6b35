import { useState } from 'react'
import { TextInput, PasswordInput, Button, Paper, Stack } from '@mantine/core'
import { useAuth } from '@/context/useAuth'

interface LoginFormProps {
  onSuccess?: () => void
}

const LoginForm = ({ onSuccess }: LoginFormProps) => {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const { login, loading } = useAuth()

  const handleLogin = async () => {
    const success = await login(username, password)
    if (success && onSuccess) {
      onSuccess()
    }
  }

  return (
    <div className="flex items-center justify-center h-screen">
      <Paper shadow="md" p="xl" className="w-96">
        <Stack>
          <TextInput
            label="用户名"
            placeholder="请输入用户名"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
          />
          <PasswordInput
            label="密码"
            placeholder="请输入密码"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <Button onClick={handleLogin} fullWidth loading={loading}>
            登录
          </Button>
        </Stack>
      </Paper>
    </div>
  )
}

export default LoginForm
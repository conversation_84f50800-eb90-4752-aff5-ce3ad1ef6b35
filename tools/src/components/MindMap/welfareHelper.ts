import { health100, ruici, newHlIdMap } from './data'
import { MindMapData } from './hooks'

export type Welfare = {
  id: string
  type: string
  title: string
  ruleMaps: RuleMap[]
  vendorMaps: VendorMap[]
  rules: ProductRule[]
}

interface ProductRule {
  items: CheckItem[]
  info: {
    id: string
    title: string
  }
}

interface CheckItem {
  id: string
  name: string
  items?: HlItem[]
  baseItems: {
    vendorid: string
    items: HlItem[]
  }[]
}

type HlItem = {
  id: string
  name: string
}

interface VendorMap {
  vendorid: string
  mappings: { hlid: string; id: string }[]
}

interface RuleMap {
  ruleid: string
  vendorMaps: VendorMap[]
}

export function genWelfareMapData(welfare: Welfare) {
  // 获取所有 HlItem 作为中心节点。由于mappings中的映射可能不是所有hlid，所以从rule中遍历
  const centerNodes = welfare.rules.flatMap((rule) =>
    rule.items.flatMap((item) => item.items || [])
  )

  // json数组health100和ruici转成对象，id-object
  const health100Map = health100.reduce((map, item) => {
    map[item.id.toString()] = item
    return map
  }, {} as Record<string, (typeof health100)[number]>)
  const ruiciMap = ruici.reduce((map, item) => {
    map[item.id.toString()] = item
    return map
  }, {} as Record<string, (typeof ruici)[number]>)

  // 数据源映射
  const vendorDataMap = {
    health100: health100Map,
    haolaruici: ruiciMap,
    hebaoruici: ruiciMap,
  } as const

  const ruleVendorMaps =
    welfare.ruleMaps?.find((rule) => rule.ruleid === welfare.rules[0]?.info.id)
      ?.vendorMaps || []
  const mergedVendorMaps = welfare.vendorMaps.map((vendor) => ({
    ...vendor,
    mappings: [
      ...vendor.mappings,
      ...(ruleVendorMaps.find((rule) => rule.vendorid === vendor.vendorid)
        ?.mappings || []),
    ],
  }))
  console.log('mergedVendorMaps', mergedVendorMaps)

  return centerNodes.map((hlItem) => {
    // 构建右分支：每个 vendorMap 对应一个节点
    const rightBranch = mergedVendorMaps
      .filter((vendor) =>
        ['health100', 'haolaruici', 'hebaoruici'].includes(vendor.vendorid)
      )
      .map((vendor) => {
        // 在团单 vendorMap 中找到与 hlItem.id 匹配的映射
        const mapping = vendor.mappings.find((m) => m.hlid === hlItem.id)
        return {
          name: (mapping?.id || '')
            .split('|')
            .map(
              (id) =>
                vendorDataMap[vendor.vendorid as keyof typeof vendorDataMap]?.[
                  id
                ]?.name || ''
            )
            .join('|'),
          id: mapping?.id || '',
          vendoid: vendor.vendorid,
        }
      })

    // 构建左分支：根据右分支vendor mappings的 id 找到对应数据源中的 hlId
    const leftBranch: { id: string; name: string; vendoid: string }[] = []
    // const newHLIds = new Set<string>()
    const newHLIdsGroup: Record<string, Set<string>> = {}
    rightBranch.forEach((right) => {
      const ids = right.id.split('|')
      const sourceData =
        vendorDataMap[right.vendoid as keyof typeof vendorDataMap]
      if (!sourceData) return { vendorid: right.vendoid, hlIds: [] }

      const hlIds = ids.map((id) => sourceData[id]?.hlId || []).flat()
      hlIds.forEach((id) => {
        if (newHLIdsGroup[right.vendoid]?.has(id)) return
        if (!newHLIdsGroup[right.vendoid]) newHLIdsGroup[right.vendoid] = new Set<string>()
        newHLIdsGroup[right.vendoid].add(id)
        leftBranch.push({
          name: newHlIdMap[id]?.name || '',
          id,
          vendoid: right.vendoid,
        })
      })
    })

    return {
      center: {
        name: hlItem.name,
        id: hlItem.id,
      },
      leftBranch,
      rightBranch,
    }
  })
}

// 更新福利规则中的映射关系
export const updateWelfareRuleWithMappings = (
  welfare: Welfare,
  mindMapData: MindMapData[]
) => {
  const { rules, id, ...ret } = welfare

  // 构建旧id和新id在团单配置中一对多的映射关系查找表
  const mappingTable = new Map<string, string[]>()
  const mappingTableGroup = new Map<string, unknown>()
  mindMapData.forEach((map) => {
    if (map.center?.id && map.leftBranch) {
      mappingTable.set(map.center.id, [
        ...new Set(map.leftBranch.map((item) => item.id)),
      ])
      mappingTableGroup.set(
        map.center.id,
        map.leftBranch.reduce((acc, cur) => {
          if (!acc[cur.vendoid]) acc[cur.vendoid] = []
          acc[cur.vendoid].push(cur.id)
          return acc
        }, {} as Record<string, string[]>)
      )
    }
  })

  const newRules = rules.map((rule) => {
    rule.items = rule.items.map((pack) => {
      const { items, ...rest } = pack
      if (!items?.length) return pack

      const vendorHlIds: Record<string, Set<string>> = {}
      const vendorNewItems: Record<string, HlItem[]> = {}
      items?.forEach((item) => {
        const mappedIdsGroup = mappingTableGroup.get(item.id)
        if (mappedIdsGroup) {
          Object.entries(mappedIdsGroup as Record<string, string[]>).forEach(([vendorid, ids]) => {
            if (!vendorHlIds[vendorid]) vendorHlIds[vendorid] = new Set<string>([])
            if (!vendorNewItems[vendorid]) vendorNewItems[vendorid] = []
            ids.forEach((id) => {
              if (vendorHlIds[vendorid].has(id)) return
              vendorNewItems[vendorid].push({
                ...item,
                id,
                name: newHlIdMap[id]?.name || '',
              })
              vendorHlIds[vendorid].add(id)
            })
          })
        } else {
          throw new Error(`hlId: ${item.id} ${item.name} 没有映射关系`)
        }
      })
      const hasSameElements = checkIdsExceptBreakfast(vendorHlIds)
      if (!hasSameElements) {
        return {
          ...rest,
          baseItems: Object
            .entries(vendorNewItems)
            .map(([vendorid, items]) => ({
              vendorid,
              items,
            })),
        }
      }

      const maxLengthItems = Object.values(vendorNewItems).reduce((max, curr) => 
        curr.length > max.length ? curr : max, 
        [] as HlItem[]
      )
      return { ...rest, baseItems: [{ vendorid: 'default', items: maxLengthItems }] }
    })

    return { ...rule, info: { ...rule.info, usePartnerItem: true } }
  })

  return { id, ...ret, rules: newRules }
}

/**
 * 检查对象中所有Set集合是否包含相同的元素
 * @param sets 包含多个Set的对象
 * @returns 如果所有Set包含相同的元素返回true，否则返回false
 */
function checkIdsExceptBreakfast(sets: Record<string, Set<string>>): boolean {
  const keys = Object.keys(sets)
  if (keys.length <= 1) return true

  const firstSetElements = [...sets[keys[0]]].filter(id => id !== 'HLZXX0001')

  return keys.every(key => {
    const currentSet = new Set([...sets[key]].filter(id => id !== 'HLZXX0001'))
    if (currentSet.size !== firstSetElements.length) return false
    return firstSetElements.every(element => currentSet.has(element))
  })
}

export const processedBaseItems = (items: CheckItem[]) => {
  return items.map((item) => {
    const baseItemsResult =
      item.baseItems[0]?.vendorid === 'default'
        ? genDefBaseItemIds(item.baseItems)
        : genVendorBaseItemIds(item.baseItems)

    return {
      id: item.id,
      name: item.name,
      ...baseItemsResult,
    }
  })
}

function genDefBaseItemIds(baseItems: CheckItem['baseItems']) {
  return {
    hlIds: baseItems[0].items.map((i) => i.id).join('|')
  }
}

function genVendorBaseItemIds(baseItems: CheckItem['baseItems']) {
  return baseItems.reduce((acc, cur) => {
    acc[cur.vendorid] = cur.items.map((i) => i.id).join('|')
    return acc
  }, {} as Record<string, string>)
}
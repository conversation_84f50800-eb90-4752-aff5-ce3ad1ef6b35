import { useCallback, useRef, useState } from 'react'

export interface MindMapData {
  center: {
    id: string
    name: string
  }
  leftBranch: Array<{
    id: string
    name: string
  } & Record<string, string>>
  rightBranch: Array<{
    id: string
    name: string
  }>
}

export const useDragEvent = (data: MindMapData, onChange?: (data: MindMapData) => void) => {
  const [draggedNode, setDraggedNode] = useState<{
    type: 'left' | 'right'
    index: number
    startY: number
    currentTransformY: number
  } | null>(null)

  const lastMoveTime = useRef(0)
  const throttleDelay = 16

  const handleMouseDown = useCallback((
    event: React.MouseEvent,
    type: 'left' | 'right',
    index: number,
    currentTransformY: number
  ) => {
    event.preventDefault()
    setDraggedNode({
      type,
      index,
      startY: event.clientY,
      currentTransformY
    })

    // 添加全局事件监听
    const handleGlobalMouseMove = (e: MouseEvent) => {
      const now = Date.now()
      if (now - lastMoveTime.current < throttleDelay) return
      e.preventDefault()

      setDraggedNode(prev => {
        if (!prev) return null
        return {
          ...prev,
          currentTransformY: currentTransformY + (e.clientY - event.clientY)
        }
      })
      
      lastMoveTime.current = now
    }

    const handleGlobalMouseUp = (e: MouseEvent) => {
      if (!onChange) return

      const newData = { ...data }
      const branch = type === 'left' ? newData.leftBranch : newData.rightBranch

      if (branch) {
        const deltaY = e.clientY - event.clientY
        const newIndex = Math.round(deltaY / 60) + index

        if (newIndex !== index && newIndex >= 0 && newIndex < branch.length) {
          const [movedNode] = branch.splice(index, 1)
          branch.splice(newIndex, 0, movedNode)
          onChange(newData)
        }
      }

      setDraggedNode(null)
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }

    document.addEventListener('mousemove', handleGlobalMouseMove)
    document.addEventListener('mouseup', handleGlobalMouseUp)
  }, [data, onChange])

  // 删除节点
  const handleDeleteNode = (type: 'left' | 'right', index: number) => {
    if (!onChange) return
    const newData = { ...data }
    if (type === 'left' && newData.leftBranch) {
      newData.leftBranch.splice(index, 1)
    } else if (type === 'right' && newData.rightBranch) {
      newData.rightBranch.splice(index, 1)
    }
    onChange(newData)
  }

  //增加节点
  const handleAddNode = (type: 'left' | 'right', index: number) => {
    if (!onChange) return

    const newData = { ...data }
    const branch = type === 'left' ? 'leftBranch' : 'rightBranch'
    const newNode = { id: '', name: '' }

    if (!newData[branch]) {
      newData[branch] = []
    }

    // 在指定位置后插入新节点
    newData[branch] = [
      ...newData[branch].slice(0, index + 1),
      newNode,
      ...newData[branch].slice(index + 1)
    ]

    onChange(newData)
  }

  return {
    draggedNode,
    handleMouseDown,
    handleDeleteNode,
    handleAddNode
  }
}
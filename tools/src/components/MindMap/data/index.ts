import health100 from './meinian.json'
import ruici from './ruici.json'
import newHlId from './zxx.json'

export interface ZXXItem {
  id: string
  name: string
  category: string
  regexp?: string
  detail?: string
  introduction?: string
  alias?: string[]
  disabled?: boolean
  replaceWith?: string[]
  replaceRemark?: string
}

const newHlIdMap = newHlId.reduce((pre, cur) => {
  pre[cur.id] = cur
  return pre
}, {} as Record<string, ZXXItem>)

export {
  health100,
  ruici,
  newHlIdMap
}
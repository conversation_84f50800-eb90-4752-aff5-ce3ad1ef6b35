import { useRef, useEffect, useState } from 'react'
import ConnectingLine from '@/components/Line'
import { MindMapData, useDragEvent } from './hooks'
import { Position, getConnections, Connection } from './helper'

interface MindMapProps {
  data: MindMapData
  onChange?: (data: MindMapData) => void
}

const MindMap = ({ data, onChange }: MindMapProps) => {
  const [connections, setConnections] = useState<Connection[]>([])
  const nodeRefs = useRef<{ [key: string]: SVGGElement | null }>({})
  const [viewBox, setViewBox] = useState("-200 -50 540 150")

  const {
    draggedNode,
    handleMouseDown,
    handleDeleteNode,
    handleAddNode
  } = useDragEvent(data, onChange)

  useEffect(() => {

    const updatePositions = () => {
      // 获取所有节点的 BBox
      const centerBox = nodeRefs.current.center?.getBBox()
      const leftBoxes = data.leftBranch?.map((_, i) => nodeRefs.current[`left-${i}`]?.getBBox()) || []
      const rightBoxes = data.rightBranch?.map((_, i) => nodeRefs.current[`right-${i}`]?.getBBox()) || []
      const leftTotalHeight = leftBoxes.reduce((sum, box) => sum + (box?.height || 0), 0)
      const rightTotalHeight = rightBoxes.reduce((sum, box) => sum + (box?.height || 0), 0)

      // 计算高度：节点总高度 + 节点间距 + 上下边距
      const nodeGap = 20 // 节点间距
      const padding = 50 // 上下边距
      const height = Math.max(150,
        Math.max(
          leftTotalHeight + (leftBoxes.length - 1) * nodeGap,
          rightTotalHeight + (rightBoxes.length - 1) * nodeGap
        ) + padding
      )

      // 计算宽度：左右各180的偏移 + 中心节点宽度 + 左右各20的边距
      const width = 400 + (centerBox?.width || 120)
      // x 起点：左侧节点偏移 + 边距
      const x = -200
      // y 起点，垂直居中
      const y = -height / 2.5

      setViewBox(`${x} ${y} ${width} ${height}`)

      const newPositions = {
        center: centerBox,
        left: leftBoxes,
        right: rightBoxes
      }
      setConnections(getConnections(newPositions as Position))
    }

    updatePositions()
  }, [data])


  const renderNode = (
    node: { id?: string; name?: string },
    ref: string,
  ) => {
    const ids = (node.id || '').split('|')
    const names = (node.name || '').split('|')
    const height = Math.max(40, ids.length * 24 + 15) // 基础高度 + 每行文字高度

    // 计算文字的起始位置，使整体垂直居中
    const lineHeight = 12 // 行高
    const totalTextHeight = ids.length * lineHeight * 2
    const startY = Math.max(20, totalTextHeight - 20)

    return (
      <g ref={el => nodeRefs.current[ref] = el} transform={`translate(0, ${40 - height})`}>
        <rect
          className="fill-white stroke-gray-200 shadow-md hover:shadow-lg transition-shadow"
          rx="8"
          ry="8"
          width="120"
          height={height}
        />
        <text
          x="60"
          y={startY}
          textAnchor="middle"
          dominantBaseline="middle"
          className="text-xs font-medium fill-slate-500"
        >
          {ids.map((id, index) => [
            <tspan
              key={`name_${index}_${id}`}
              x="60"
              dy={index === 0 ? -(ids.length * 1.4 - 1) * 12 : 12}
              className="text-[10px] fill-slate-600"
            >
              {names[index]}
            </tspan>,
            <tspan
              key={`id_${index}_${id}`}
              x="60"
              dy={12}
              className="text-[10px] fill-slate-600"
            >
              {id}
            </tspan>
          ])}
        </text>
      </g>
    )
  }

  const renderBranch = (branch: Array<{ id: string; name: string }>, direction: 'left' | 'right') => (
    <g>
      {branch.map((node, index) => {
        const defaultTransformY = index * 60 - (branch.length - 1) * 30
        const transformY = draggedNode?.index === index && draggedNode?.type === direction
          ? draggedNode.currentTransformY
          : defaultTransformY
        return (
          <g
            key={index}
            className="cursor-grab"
            onMouseDown={e => index !== undefined && handleMouseDown(e, direction, index, defaultTransformY)}
            transform={`translate(${direction === 'left' ? -180 : 180}, ${transformY})`}
          >
            {renderNode(node, `${direction}-${index}`)}

            {/* 添加节点按钮 */}
            <g
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation()
                handleAddNode(direction, index)
              }}
            >
              <path
                transform='translate(0, -10)'
                d="M60 45 A8 8 0 0 1 60 61 A8 8 0 0 1 60 45 M56 53 L64 53 M60 49 L60 57"
                className="fill-green-500 stroke-white stroke-[1.5] opacity-0 hover:opacity-100"
              />
            </g>

            {/* 删除节点按钮 */}
            <g
              className="cursor-pointer"
              onClick={(e) => {
                e.stopPropagation()  // 阻止事件冒泡, 貌似没啥用？
                index !== undefined && handleDeleteNode(direction, index)
              }}
            >
              <path
                d="M110 2 A8 8 0 0 1 110 18 A8 8 0 0 1 110 2 M106 10 L114 10 M110 6 L110 14"
                className="fill-red-500 stroke-white stroke-[1.5] opacity-0 hover:opacity-100"
                transform="rotate(45, 110, 10)"
              />
            </g>
          </g>
        )
      })}
    </g>
  )


  return (
    <svg
      className="min-h-[150px] text-slate-200"
      viewBox={viewBox}
    >
      {renderNode(data.center, 'center')}

      {data.leftBranch && renderBranch(data.leftBranch, 'left')}
      {data.rightBranch && renderBranch(data.rightBranch, 'right')}

      {connections.map((connection, i) => (
        <ConnectingLine
          key={`line-${i}`}
          start={connection.start}
          ends={connection.ends}
        />
      ))}
    </svg>
  )
}

export default MindMap
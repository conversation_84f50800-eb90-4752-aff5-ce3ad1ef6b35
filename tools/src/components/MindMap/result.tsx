import { Table, Button } from '@mantine/core'
import { useMemo } from 'react'

import { MindMapData } from './hooks'
import { downloadExcel, downloadJson } from '@/utils/excel'
import { createMapping } from './helper'

interface ResultTableProps {
  data: MindMapData[]
}

const ResultTable = ({ data }: ResultTableProps) => {
  // const rows: TableRow[] = data.flatMap(item => createMapping(item))
  const rows = useMemo(() => {
    return data.flatMap(item => createMapping(item))
  }, [data])

  return (
    <>
      <div className='flex'>
        <Button className='mr-5' variant="light" onClick={() => downloadExcel(rows, Object.keys(rows[0]), '最小项映射')}>下载最小项映射Excel</Button>
        <Button className='mr-5' variant="light" onClick={() => downloadJson(rows, '最小项映射')}>下载最小项映射JSON</Button>
      </div>

      <Table striped withColumnBorders stickyHeader>
        <Table.Caption>好啦新旧最小项映射表</Table.Caption>
        <Table.Thead>
          <Table.Tr>
            <Table.Th>旧id</Table.Th>
            <Table.Th>旧名称</Table.Th>
            <Table.Th>新id</Table.Th>
            <Table.Th>新名称</Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {rows.map((row, index) => (
            <Table.Tr key={index}>
              <Table.Td>{row.hlCode}</Table.Td>
              <Table.Td>{row.hlCodeName}</Table.Td>
              <Table.Td>{row.hlId || ''}</Table.Td>
              <Table.Td>{row.hlItemName || ''}</Table.Td>
            </Table.Tr>
          ))}
        </Table.Tbody>
      </Table>
    </>
  )
}

export default ResultTable

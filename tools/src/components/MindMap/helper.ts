import { MindMapData } from './hooks'

export interface NodePosition {
  x: number
  y: number
  width: number
  height: number
}

export type Position = {
  center?: NodePosition
  left: NodePosition[]
  right: NodePosition[]
}

export type Connection = {
  start: {
      x: number;
      y: number;
  };
  ends: {
      x: number;
      y: number;
  }[];
}

export const getConnections = (positions: Position): Connection[] => {
  if (!positions.center) return []

  const connections = []
  // 根据左右分支计算中心点X坐标
  const centerY = positions.center.height / 2

  // 处理左侧连接
  if (positions.left.length) {
    connections.push({
      start: { x: 0, y: centerY },
      ends: positions.left.map((pos, index) => ({
        x: pos.x + pos.width -180,
        y: pos.y + pos.height / 2 + index * 60 - (positions.left.length - 1) * 30
      }))
    })
  }

  if (positions.right.length) {
    const centerX = positions.center.width
    connections.push({
      start: { x: centerX, y: centerY },
      ends: positions.right.map((pos, index) => {
        const defaultY = pos.y + index * 60 - (positions.right.length - 1) * 30
        const transformY = 40 - pos.height //节点默认高度是40，大于40的多行文本节点需要向上偏移
        return {
          x: pos.x + 180,
          y: defaultY + pos.height / 2 + transformY
        }
      })
    })
  }

  return connections
}

export function createMapping(data: MindMapData) {
  const rightItems = data.rightBranch || []
  const leftItems = data.leftBranch || []
  
  return rightItems.map((right, index) => {
    /**
     * 一般情况下，直接使用相同索引的左侧元素作为映射
     * 如果有左侧和右侧虽然长度一样，但顺序不对，可在页面调整、增加或删除节点使左右匹配
    */
    if (index < leftItems.length) {
      return {
        hlCode: right.id,
        hlCodeName: right.name || '',
        hlId: leftItems[index].id,
        hlItemName: leftItems[index].name
      }
    }
    
    /**
     * 如果右侧多个旧的id可以映射到左侧的一个，当右侧元素多于左侧时，使用左侧最后一个元素作为映射
    */
    const leftIndex = Math.max(leftItems.length - 1, 0)
    return {
      hlCode: right.id,
      hlCodeName: right.name || '',
      hlId: leftIndex >= 0 ? leftItems[leftIndex].id : undefined,
      hlItemName: leftIndex >= 0 ? leftItems[leftIndex].name : undefined
    }
  })
}

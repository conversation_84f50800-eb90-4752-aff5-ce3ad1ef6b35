
interface Point {
  x: number
  y: number
}

interface ConnectingLineProps {
  start: Point
  ends: Point[]
  strokeWidth?: number
  color?: string
}

const ConnectingLine = ({
  start,
  ends,
  strokeWidth = 1,
}: ConnectingLineProps) => {
  const generatePath = (start: Point, end: Point) => {
    const midX = start.x + (end.x - start.x) / 2

    return `
      M ${start.x} ${start.y}
      L ${midX} ${start.y}
      Q ${midX} ${start.y} ${midX} ${start.y + (end.y - start.y) / 2}
      L ${midX} ${end.y}
      Q ${midX} ${end.y} ${end.x} ${end.y}
    `
  }

  return (
    <>
      {ends.map((end, index) => (
        <g key={index}>
          <path
            d={generatePath(start, end)}
            fill="none"
            stroke="currentColor"
            strokeWidth={strokeWidth}
            className="transition-all duration-300 ease-in-out"
          />
        </g>
      ))}
    </>
  )
}

export default ConnectingLine
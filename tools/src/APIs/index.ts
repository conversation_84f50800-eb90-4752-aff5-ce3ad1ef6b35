import { keyExists, sleep, wrapPromise } from '@/utils/share'

enum Methods {
  POST = 'POST',
  GET = 'GET',
  PATCH = 'PATCH',
  PUT = 'PUT',
}

/**
 * 接口域名：https://www.ihaola.com.cn
 */
const hlApis = {
  hlItems: {
    url: '/h5/pev2svc/forpe/getAllBaseItems',
    method: Methods.GET
  }
}

/**
 * 接口域名：https://pe.ihaola.com.cn
 */
const peApis = {
  modifyVoucher: {
    url: '/accounting/modifyproduct',
    method: Methods.POST,
  },
  modifyExpress: {
    url: '/accounting/modifycustomizedpack',
    method: Methods.POST,
  },
  usernameLogin: {
    url: '/accounting/usernamelogin',
    method: Methods.POST,
  },
  sessionLogin: {
    url: '/accounting/sessionlogin',
    method: Methods.POST,
  },
  queryData: {
    url: '/accounting/querydataex?token=mc3r262DWR-ttjk00AS091&&showdetail=show',
    method: Methods.POST
  },
 mongoData: {
    url: '/accounting/fetchmongodata',
    method: Methods.POST
  },
  mongoCollections: {
    url: '/accounting/fetchmongocollections',
    method: Methods.POST
  },
  mongoSchema: {
    url: '/accounting/fetchmongoschema',
    method: Methods.POST
  },

  // 获取所有乙方
  allVendors: {
    url: '/accounting/partnerContractConfig/orderConsumers',
    method: Methods.GET
  },

  //合同已有甲方
  contractPartners: {
    url: '/accounting/partnerContractConfig/orderProducers',
    method: Methods.GET
  },
  //新增甲方
  addContractPartner: {
    url: '/accounting/partnerContractConfig/orderProducer',
    method: Methods.PUT
  },

  /**
   * 甲方合同
  */
  partnerContracts: {
    url: '/accounting/partnerContractConfig/orderProducerContracts',
    method: Methods.GET
  },

  addContract: {
    url: '/accounting/partnerContractConfig/orderProducerContract',
    method: Methods.PUT
  },
  updateContract: {
    url: '/accounting/partnerContractConfig/orderProducerContract/:_id',
    method: Methods.PATCH
  },

  /**
   * 甲方项目
  */
  partnerItems: {
    url: '/accounting/partnerContractConfig/orderProducerItems',
    method: Methods.GET
  },
  
  addPartnerItem: {
    url: '/accounting/partnerContractConfig/orderProducerItem',
    method: Methods.PUT
  },
  updatePartnerItem: {
    url: '/accounting/partnerContractConfig/orderProducerItem/:_id',
    method: Methods.PATCH
  },
}

export const APIs = {
  ...hlApis,
  ...peApis,
} as const

type CustomResponse<T> = {
  code?: number
  message?: string
  msg?: string
} & T

type RequestArgs = {
  requestData?: Record<string, unknown>
  queries?: Record<string, string>
  retry?: number
  retryDelay?: number
  callback?: (params: unknown) => void
}

const reqList: string[] = []
const allowRequest = function(url: string) {
  for (let i = 0; i < reqList.length; i++) {
    if (url.indexOf(reqList[i]) > -1) {
      reqList.splice(i, 1)
      break
    }
  }
}

export async function wrapFetch<T>(
  api: ApiName,
  args?: RequestArgs & { repeatable?: boolean }
): Promise<[Response | null, CustomResponse<T> | null]>{
  const controller = new AbortController()
  const signal = controller.signal

  const { retry, requestData, queries, repeatable } = args || {}
  const { url, method } = initFetchUrl(api, queries)
  let count = 0,
    needRetry = retry || false,
    body
  while (count === 0 || (needRetry && retry && count < retry)) {
    count++
    const { search, ...params } = initFetchParams(method, requestData, signal)
    if (!repeatable) {
      if (reqList.indexOf(url) > -1) {
        return [new Response(JSON.stringify({ message: '请求重复被中断' }), {
          status: 499,
          statusText: 'Request Aborted',
          headers: {
            'Content-Type': 'application/json'
          }
        }), null]
      }
      reqList.push(url)
    }
    
    const [err, res] = await wrapPromise(fetch(`${url}${search}`, params))

    if (err || !res?.ok) {
      needRetry = false
      return [err, null]
    }
    const isImage = res.headers.get('Content-Type')?.includes('image')
    body = isImage ? await res.text() : await res.json()

    if (!body || (!isImage && body.code !== 0 && body.code !== 200)) {
      controller.abort()
      if (needRetry && retry) {
        needRetry = retry >= count
        await sleep(2000)
      }
    } else {
      needRetry = false
    }
  }
  setTimeout(() => allowRequest(url), 1000)
  return [null, body]
}
type Params = {
  method: Methods
  credentials: 'include'
  signal: AbortSignal
  headers: {
    'Content-Type': string
  }
  body?: string
  search: string
  MODE: string
}
function initFetchParams(
  method: Methods,
  data: Record<string, unknown> | undefined,
  signal: AbortSignal
): Params {
  const body = method === 'GET' ? {} : { body: JSON.stringify(data) }
  const search = !data ? '' : method === 'GET' ? genSearchParams(data) : ''
  return {
    method,
    MODE: 'same-origin',
    credentials: 'include',

    signal,
    headers: {
      'Content-Type': 'application/json',
    },
    ...body,
    search,
  }
}

const genSearchParams = (data: Record<string, unknown>) => {
  return Object.keys(data).reduce((str, key) => {
    str += `${key}=${data[key]}`
    return str
  }, '?')
}

function initFetchUrl(
  urlName: ApiName,
  params: Record<string, string> = {}
): { url: string; method: Methods } {
  const prefix = import.meta.env.VITE_API_PREFIX
  const prefix2 = import.meta.env.VITE_DOMAIN_PREFIX
  const target = import.meta.env.VITE_DOMAIN_TARGET

  const { url, method } = APIs[urlName]
  if (keyExists(hlApis, urlName)) {
    return { url: `${target}${prefix2}${url}`, method }
  }

  return {
    url: `${prefix}${url.replace(/:(\w+)/g, (_, key) => params[key])}`,
    method,
  }
}

type APIMap = typeof APIs
export type ApiName = keyof APIMap

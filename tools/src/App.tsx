import { useEffect } from 'react'

import { useAtom } from 'jotai'

import { ThemeProvider } from '@/context/ThemeProvider'
import { AuthProvider } from '@/context/AuthContext'
import { Routes } from './routes'
import { fetchPartnerGroupsAtom } from '@/store/partners'

import './App.css'

export default function CollapseDesktop() {
  const [, fetchPartners] = useAtom(fetchPartnerGroupsAtom)

  useEffect(() => {
    fetchPartners()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  return (
    <ThemeProvider>
      {/* 路由组件，部分可能需要登录鉴权 */}
      <AuthProvider>
        <Routes />
      </AuthProvider>
    </ThemeProvider>

  )
}
/* eslint-disable @typescript-eslint/no-explicit-any */
import { read, utils, writeFile } from 'xlsx'

export const excelHandler = async (file: File, callback: (...arg: any) => void) => {
  const reader = new FileReader()
  reader.readAsArrayBuffer(file)
  reader.onload = function (e) {
    const data = e.target?.result
    if (data instanceof ArrayBuffer) {
      const wb = read(new Uint8Array(data), { type: 'array' })
      const orderJSON = wb.SheetNames.map(sheetName => utils.sheet_to_json(wb.Sheets[sheetName]))
      callback(orderJSON)
    }
  }
}

function filterData(order: Record<string, string>, header: string[]) {
  if (!order) return {}
  return header.reduce((obj, name) => {
    obj[name] = order[name]
    return obj
  }, {} as Record<string, string>)
}

export function downloadExcel(data: any[], headers: string[], name: string = 'jsonToExcel') {
  const result = data.map((v: Record<string, string>) => filterData(v, headers))
  const worksheet = utils.json_to_sheet(result)
  const workbook = utils.book_new()
  utils.book_append_sheet(workbook, worksheet)
  writeFile(workbook, `${name}${new Date().toLocaleDateString()}.xlsx`)
}

export function downloadJson(data: any, filename: string = 'data.json') {
  const jsonStr = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}
import { notifications } from '@mantine/notifications'

export async function copyToClipboard(text: string): Promise<void> {
  try {
    await navigator.clipboard.writeText(text)
    notifications.show({
      message: '复制成功🌟',
      position: 'top-center',
      autoClose: 300,
    })
  } catch (err) {
    console.error("Failed to copy text to clipboard: ", err)
  }
}

export function excelDateToJSDate(serial: number) {
  const utc_days = Math.floor(serial - 25569)
  const utc_value = utc_days * 86400
  const date_info = new Date(utc_value * 1000)

  const fractional_day = serial - Math.floor(serial) + 0.0000001

  let total_seconds = Math.floor(86400 * fractional_day)

  const seconds = total_seconds % 60

  total_seconds -= seconds

  const hours = Math.floor(total_seconds / (60 * 60))
  const minutes = Math.floor(total_seconds / 60) % 60

  return new Date(
    date_info.getFullYear(),
    date_info.getMonth(),
    date_info.getDate(),
    hours,
    minutes,
    seconds
  )
}

export function wrapPromise<T>(promise: Promise<T>): Promise<(T | null)[]> {
  return promise.then((res) => [null, res]).catch((err) => [err, null])
}

export function sleep(delay: number, sleepReason?: string): Promise<unknown> {
  return new Promise((resolve) => setTimeout(() => resolve(sleepReason), delay))
}

export function keyExists<T extends object | Element>(
  obj: T,
  k: string
): k is keyof T & string {
  return k in obj
}

export const genJsonTree = (json: unknown, key?: string, separator = '##') => {
  const convertToTreeNode = (
    value: unknown,
    key?: string,
    parentPath = ''
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): { value: string, label: string, children?: any[] } => {
    const label = key || 'root'
    const currentPath = parentPath ? `${parentPath}.${label}` : label

    if (value === null) {
      return { value: currentPath, label: `${label}${separator}null` }
    }

    if (typeof value === 'undefined') {
      return { value: currentPath, label: `${label}${separator} undefined` }
    }

    if (typeof value !== 'object') {
      return { value: currentPath, label: `${label}${separator} ${String(value)}` }
    }

    if (Array.isArray(value)) {
      const children = value.map((item, index) =>
        convertToTreeNode(item, `${index}`, currentPath)
      )
      return {
        value: currentPath,
        label: `${label} (Array)`,
        children
      }
    }

    // 对象类型
    const children = Object.entries(value).map(([k, v]) =>
      convertToTreeNode(v, k, currentPath)
    )
    return {
      value: currentPath,
      label: `${label} (Object)`,
      children
    }
  }

  return convertToTreeNode(json, key)
}

/**
 * 字符串数组缩略展示
 * @param {string[]} items The array of strings to handle
 * @returns {string} The formatted string
*/
export const handleStringArray = (items: string[]) => {
  const len = items.length
  if (len > 1) {
    return `${items[0]}...等${len}个`
  }
  return items[0] || ''
}

/**
 * 日期格式化
 * @param {Date|string|number|null|undefined} date The date to format
 * @param {string} [format='YYYY/MM/DD'] The format of the date string
 * @returns {string} The formatted date string
 */
export const formatDate = (
  date: Date | string | number | null | undefined,
  format: string = 'YYYY/MM/DD'
): string => {
  if (!date) return ''

  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  const hours = d.getHours()
  const minutes = d.getMinutes()
  const seconds = d.getSeconds()

  // 月份和日期补零
  const pad2 = (num: number) => String(num).padStart(2, '0')
  const shortYear = String(year).slice(-2)

  const replacements: Record<string, string> = {
    YYYY: String(year),
    YY: shortYear,
    MM: pad2(month),
    M: String(month),
    DD: pad2(day),
    D: String(day),
    HH: pad2(hours),
    H: String(hours),
    mm: pad2(minutes),
    m: String(minutes),
    ss: pad2(seconds),
    s: String(seconds),
  }

  return format.replace(
    /YYYY|YY|MM|M|DD|D|HH|H|mm|m|ss|s/g,
    (match) => replacements[match] || match
  )
}

import { <PERSON><PERSON><PERSON>, <PERSON>tineP<PERSON>ider, <PERSON>dal, Radio, createTheme } from '@mantine/core'
import { Cross2Icon } from '@radix-ui/react-icons'
import { Notifications } from '@mantine/notifications'

import '@mantine/core/styles.css'

const theme = createTheme({
  primaryColor: 'cyan',
  colors: {
    cyan: [
      "#e3fafc",
      "#c3fae8",
      "#99e9f2",
      "#66d9e8",
      "#3bc9db",
      "#22b8cf",
      "#0cbacf", //cyan-6 改为好啦主题色
      "#1098ad",
      "#0c8599",
      "#0b7285"
    ]
  },
  components: {
    Radio: Radio.extend({
      defaultProps: {
        icon: CheckIcon,
      }
    }),
    Modal: Modal.extend({
      defaultProps: {
        closeButtonProps: {
          icon: <Cross2Icon className='size-6 text-gray-300' />,
        }
      },
    }),
  }
})

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  return (
    <MantineProvider  defaultColorScheme="light" theme={theme}>
      <Notifications />
      {children}
    </MantineProvider>
  )
}

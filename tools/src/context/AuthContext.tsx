import { useState, ReactNode, useEffect } from 'react'
import { wrapFetch } from '@/APIs'
import { AuthContext } from './useAuth'

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    console.log('provider auth')
    async function sessionLogin() {
      const [err, res] = await wrapFetch('sessionLogin')
      if (!err && res?.code === 0) {
        setIsLoggedIn(true)
      }
    }

    sessionLogin()
  }, [])

  const login = async (userName: string, userPassword: string) => {
    setLoading(true)
    const [err, res] = await wrapFetch('usernameLogin', {
      requestData: { userName, userPassword }
    })
    setLoading(false)
    
    if (!err && res?.code === 0) {
      setIsLoggedIn(true)
      return true
    }
    return false
  }

  const logout = () => {
    setIsLoggedIn(false)
  }

  return (
    <AuthContext.Provider value={{ isLoggedIn, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  )
}

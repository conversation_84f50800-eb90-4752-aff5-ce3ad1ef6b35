import { atom } from 'jotai'
import type { HLItem } from '@/store/types'
import { fetchHlItems } from '@/services/partners'
import { wrapFetch } from '@/APIs'

export const hlItemsAtom = atom<HLItem[]>([])

export const fetchHlItemsAtom = atom(
  null,
  async (_, set) => {
    const items = await fetchHlItems()
    set(hlItemsAtom, items)
  }
)

// 获取所有分类
export const categoriesAtom = atom((get) => {
  const items = get(hlItemsAtom)
  return [...new Set(items.map(item => item.category))]
})

// 获取分类下的项目数量
export const categoryCountsAtom = atom((get) => {
  const items = get(hlItemsAtom)
  const categories = get(categoriesAtom)
  
  return categories.reduce((acc, category) => {
    acc[category] = items.filter(item => item.category === category).length
    return acc
  }, {} as Record<string, number>)
})

// 获取分类下的已选项目数量
export const selectedCategoryCountsAtom = atom(
  (get) => (selectedItems: string[]) => {
    const items = get(hlItemsAtom)
    const categories = get(categoriesAtom)
    
    return categories.reduce((acc, category) => {
      acc[category] = items
        .filter(item => item.category === category && selectedItems.includes(item.id))
        .length
      return acc
    }, {} as Record<string, number>)
  }
) 

// 生成item id和name的映射
export const itemIdNameMapAtom = atom((get) => {
  const items = get(hlItemsAtom)
  return items.reduce((acc, item) => {
    acc[item.id] = item.name
    return acc
  }, {} as Record<string, string>)
})

//获取甲方所有项目
export interface PartnerItem {
  _id: string
  itemId: string
  orderConsumerId: string
  orderProducerContractId: string
  baseItems: { id: string }[]
  beginDate: string
  displayPrice: string
  endDate: string
  name: string
  sellingPrice: string
  settlementPrice: string
}

// Partner Items 相关状态
export const partnerItemsAtom = atom<PartnerItem[]>([])
export const partnerItemsLoadingAtom = atom<boolean>(false)
export const currentContractIdAtom = atom<string>('')

// 获取甲方项目数据的 atom
export const fetchPartnerItemsAtom = atom(
  null,
  async (get, set, {contractId, update = false}: {contractId: string, update?: boolean}) => {
    const currentContractId = get(currentContractIdAtom)
    
    // 如果 contractId 没有变化，则不重新请求
    if (currentContractId === contractId && !update) {
      return
    }

    if (!contractId) {
      set(partnerItemsAtom, [])
      set(currentContractIdAtom, '')
      return
    }

    set(partnerItemsLoadingAtom, true)
    
    const [err, res] = await wrapFetch<{ data: { docs: PartnerItem[] } }>('partnerItems', {
      requestData: { orderProducerContractId: contractId }
    })

    if (!err && res?.data?.docs) {
      set(partnerItemsAtom, res.data.docs)
    } else {
      set(partnerItemsAtom, [])
    }
    
    set(currentContractIdAtom, contractId)
    set(partnerItemsLoadingAtom, false)
  }
)

import { atom } from 'jotai'
import type { PartnerGroup, PartnerOption } from '@/store/types'
import { genPartnerGroups } from './utils'
import { fetchPartners } from '@/services/partners'

export const partnerGroupsAtom = atom<PartnerGroup[]>([])
export const partnersAtom = atom<PartnerOption[]>([])

export const fetchPartnerGroupsAtom = atom(
  null,
  async (_, set) => {
    const partners = await fetchPartners()
    set(partnersAtom, partners)
    const partnerGroups = genPartnerGroups(partners)
    set(partnerGroupsAtom, partnerGroups)
  }
) 
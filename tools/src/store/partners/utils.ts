import type { PartnerOption, PartnerGroup, PartnerItem } from '@/store/types'

export function genPartnerGroups(partners: PartnerOption[]): PartnerGroup[] {
  const groups = partners.reduce((acc: Record<string, PartnerItem[]>, partner) => {
    const groupName = partner.parent || '其他'
    if (!acc[groupName]) {
      acc[groupName] = []
    }
    acc[groupName].push({
      label: partner.label,
      value: partner.partnerid,
      name: partner.value,
    })
    return acc
  }, {})

  return Object.entries(groups).map(([group, items]) => ({
    group,
    items
  }))
} 
{"name": "tool", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@mantine/core": "^8.1.0", "@mantine/dates": "^8.1.0", "@mantine/dropzone": "^8.1.0", "@mantine/form": "^8.1.0", "@mantine/hooks": "^8.1.0", "@mantine/notifications": "^8.1.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-icons": "^1.3.2", "@types/node": "^22.13.10", "jotai": "^2.12.5", "mantine-datatable": "^7.17.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.3.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.39", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^3.4.6", "typescript": "^5.2.2", "vite": "^6.3.5"}}
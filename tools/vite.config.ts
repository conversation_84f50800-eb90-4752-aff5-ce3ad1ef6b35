import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import { fileURLToPath, URL } from 'node:url'
import { resolve } from 'path'

function manualChunks(id: string) {
	if (id.includes('node_modules')) {
    const path = id.match(/node_modules\/([^/]+)/)?.[1].split('-')[0]
    
    switch(path) {
      case 'xlsx':
      case '@mantine':
        return path
      default:
        return 'vendor'
    }
	}
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const viteEnv = loadEnv(mode, process.cwd())

  return {
    base: mode === 'development' ? './' : '/tools',
    plugins: [react()],
    assetsInclude: ['**/*.jpg'],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
  
    build: {
      emptyOutDir: true,
      outDir: resolve(__dirname, '../dist/www/tools'),

      rollupOptions: {
        output: {
          manualChunks
        }
      },
  
      /** 实验特性！禁止复制指定public目录资源到outDir目录 */
      copyPublicDir: false,
    },

    server: {
      proxy: {
        [viteEnv.VITE_API_PREFIX]: {
          target: viteEnv.VITE_API_URL,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(new RegExp(viteEnv.VITE_API_PREFIX), ''),
        },
        [viteEnv.VITE_DOMAIN_PREFIX]: {
          target: viteEnv.VITE_DOMAIN_URL,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(new RegExp(viteEnv.VITE_DOMAIN_PREFIX), ''),
        },
      },
      host: '0.0.0.0',
      hmr: true,
    },
  }
})
